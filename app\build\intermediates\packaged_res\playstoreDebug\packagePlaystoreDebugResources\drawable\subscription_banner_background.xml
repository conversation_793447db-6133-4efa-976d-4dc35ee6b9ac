<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Active subscription background -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/subscription_active_bg" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="@color/subscription_active_border" />
        </shape>
    </item>
    
    <!-- Expiring soon background -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/subscription_warning_bg" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="@color/subscription_warning_border" />
        </shape>
    </item>
    
    <!-- Expired background -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/subscription_expired_bg" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="@color/subscription_expired_border" />
        </shape>
    </item>
    
    <!-- Default background -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/subscription_default_bg" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="@color/subscription_default_border" />
        </shape>
    </item>
    
</selector>
