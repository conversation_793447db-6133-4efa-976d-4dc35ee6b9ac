# 🗄️ V2Hoor VPN Database Schema

## 📋 **Overview**

This document defines the complete Firebase Realtime Database structure for the V2Hoor VPN application. The database is designed to support user management, server configurations, subscriptions, usage analytics, and administrative functions.

---

## 🏗️ **Database Structure**

### **📊 Root Level Structure**
```
v2hoor-vpn/
├── users/                    # User data and configurations
├── servers/                  # Global server configurations (admin managed)
├── subscriptions/            # Subscription plans and pricing
├── analytics/                # Usage analytics and statistics
├── system/                   # System configuration and metadata
├── admin/                    # Administrative data and logs
└── backups/                  # Database backups and snapshots
```

---

## 👥 **Users Collection**

### **Structure: `/users/{uid}/`**
```json
{
  "uid": "user_unique_id",
  "profile": {
    "email": "<EMAIL>",
    "name": "User Name",
    "phone": "+1234567890",
    "avatar_url": "https://...",
    "language": "ar",
    "timezone": "Africa/Cairo",
    "created_at": 1640995200000,
    "updated_at": 1640995200000,
    "last_login": 1640995200000,
    "login_count": 42,
    "email_verified": true,
    "phone_verified": false
  },
  "role": {
    "type": "user",
    "permissions": ["read_servers", "connect_vpn"],
    "assigned_by": "admin_uid",
    "assigned_at": 1640995200000,
    "expires_at": null
  },
  "subscription": {
    "plan_id": "premium_monthly",
    "status": "active",
    "start_date": 1640995200000,
    "end_date": 1643673600000,
    "auto_renew": true,
    "payment_method": "credit_card",
    "trial_used": false,
    "subscription_type": "premium",
    "data_limit_gb": 100,
    "data_used_gb": 25.5,
    "max_devices": 5,
    "current_devices": 2,
    "features": ["unlimited_bandwidth", "premium_servers", "24_7_support"]
  },
  "servers": {
    "server_id_1": {
      "id": "server_id_1",
      "name": "US East Premium",
      "config": "vmess://...",
      "protocol": "vmess",
      "server": "us-east.v2hoor.com",
      "port": 443,
      "uuid": "12345678-1234-1234-1234-123456789012",
      "security": "auto",
      "network": "ws",
      "path": "/v2ray",
      "host": "us-east.v2hoor.com",
      "tls": "tls",
      "sni": "us-east.v2hoor.com",
      "alpn": "h2,http/1.1",
      "fingerprint": "chrome",
      "location": {
        "country": "United States",
        "city": "New York",
        "flag": "🇺🇸",
        "coordinates": {"lat": 40.7128, "lng": -74.0060}
      },
      "performance": {
        "ping": 45,
        "speed_mbps": 100,
        "load_percentage": 25,
        "uptime_percentage": 99.9
      },
      "assigned_at": 1640995200000,
      "assigned_by": "admin_uid",
      "is_active": true,
      "expires_at": 1643673600000,
      "usage_limit_gb": 50,
      "usage_used_gb": 12.3,
      "created_at": 1640995200000,
      "updated_at": 1640995200000
    }
  },
  "stats": {
    "sessions": {
      "session_id_1": {
        "session_id": "session_id_1",
        "server_id": "server_id_1",
        "server_name": "US East Premium",
        "start_time": 1640995200000,
        "end_time": 1640998800000,
        "duration_seconds": 3600,
        "bytes_sent": 1048576,
        "bytes_received": 5242880,
        "connection_type": "wifi",
        "device_info": {
          "model": "Samsung Galaxy S21",
          "os": "Android 12",
          "app_version": "1.0.0"
        },
        "disconnect_reason": "user",
        "quality_rating": 5,
        "created_at": 1640995200000
      }
    },
    "summary": {
      "total_sessions": 150,
      "total_duration_seconds": 540000,
      "total_bytes_sent": 157286400,
      "total_bytes_received": 786432000,
      "average_session_duration": 3600,
      "most_used_server": "server_id_1",
      "most_used_connection": "wifi",
      "last_updated": 1640995200000
    },
    "daily": {
      "2024-01-01": {
        "sessions": 5,
        "duration_seconds": 18000,
        "bytes_sent": 5242880,
        "bytes_received": 26214400,
        "unique_servers": 2
      }
    },
    "monthly": {
      "2024-01": {
        "sessions": 150,
        "duration_seconds": 540000,
        "bytes_sent": 157286400,
        "bytes_received": 786432000,
        "unique_servers": 5
      }
    }
  },
  "preferences": {
    "theme": "dark",
    "language": "ar",
    "auto_connect": true,
    "preferred_protocol": "vmess",
    "kill_switch": true,
    "dns_servers": ["*******", "*******"],
    "notifications": {
      "connection_status": true,
      "data_usage": true,
      "subscription_expiry": true,
      "new_servers": false
    },
    "security": {
      "biometric_enabled": true,
      "pin_enabled": false,
      "auto_lock_timeout": 300
    }
  },
  "devices": {
    "device_id_1": {
      "id": "device_id_1",
      "name": "Samsung Galaxy S21",
      "type": "android",
      "os_version": "Android 12",
      "app_version": "1.0.0",
      "last_seen": 1640995200000,
      "is_active": true,
      "registered_at": 1640995200000
    }
  },
  "payments": {
    "payment_id_1": {
      "id": "payment_id_1",
      "amount": 9.99,
      "currency": "USD",
      "method": "credit_card",
      "status": "completed",
      "subscription_id": "premium_monthly",
      "transaction_id": "txn_123456789",
      "created_at": 1640995200000,
      "processed_at": 1640995200000
    }
  },
  "support": {
    "tickets": {
      "ticket_id_1": {
        "id": "ticket_id_1",
        "subject": "Connection Issues",
        "status": "open",
        "priority": "medium",
        "category": "technical",
        "messages": [
          {
            "id": "msg_1",
            "sender": "user",
            "message": "I'm having trouble connecting to servers",
            "timestamp": 1640995200000,
            "attachments": []
          }
        ],
        "assigned_to": "support_agent_1",
        "created_at": 1640995200000,
        "updated_at": 1640995200000
      }
    }
  }
}
```

---

## 🖥️ **Servers Collection**

### **Structure: `/servers/{server_id}/`**
```json
{
  "id": "server_id_1",
  "name": "US East Premium",
  "description": "High-speed server in New York",
  "status": "active",
  "type": "premium",
  "protocol": "vmess",
  "config": {
    "server": "us-east.v2hoor.com",
    "port": 443,
    "uuid": "12345678-1234-1234-1234-123456789012",
    "security": "auto",
    "network": "ws",
    "path": "/v2ray",
    "host": "us-east.v2hoor.com",
    "tls": "tls",
    "sni": "us-east.v2hoor.com",
    "alpn": "h2,http/1.1",
    "fingerprint": "chrome"
  },
  "location": {
    "country": "United States",
    "country_code": "US",
    "city": "New York",
    "region": "North America",
    "flag": "🇺🇸",
    "coordinates": {
      "latitude": 40.7128,
      "longitude": -74.0060
    }
  },
  "performance": {
    "ping_ms": 45,
    "speed_mbps": 1000,
    "load_percentage": 25,
    "uptime_percentage": 99.9,
    "last_tested": 1640995200000
  },
  "capacity": {
    "max_users": 1000,
    "current_users": 250,
    "bandwidth_limit_gbps": 10,
    "bandwidth_used_gbps": 2.5
  },
  "access": {
    "subscription_types": ["free", "premium"],
    "user_limit": 1000,
    "data_limit_gb": null,
    "speed_limit_mbps": null,
    "allowed_countries": ["*"],
    "blocked_countries": ["CN", "IR"]
  },
  "maintenance": {
    "scheduled_maintenance": null,
    "last_maintenance": 1640995200000,
    "maintenance_window": "02:00-04:00 UTC"
  },
  "created_at": 1640995200000,
  "updated_at": 1640995200000,
  "created_by": "admin_uid"
}
```

---

## 💳 **Subscriptions Collection**

### **Structure: `/subscriptions/{plan_id}/`**
```json
{
  "id": "premium_monthly",
  "name": "Premium Monthly",
  "description": "Full access to all premium features",
  "type": "premium",
  "duration_days": 30,
  "price": {
    "amount": 9.99,
    "currency": "USD",
    "billing_cycle": "monthly"
  },
  "features": {
    "unlimited_bandwidth": true,
    "premium_servers": true,
    "max_devices": 5,
    "data_limit_gb": null,
    "speed_limit_mbps": null,
    "support_level": "priority",
    "ad_free": true,
    "kill_switch": true,
    "split_tunneling": true
  },
  "server_access": {
    "free_servers": true,
    "premium_servers": true,
    "exclusive_servers": false,
    "server_locations": ["*"]
  },
  "trial": {
    "available": true,
    "duration_days": 7,
    "requires_payment_method": true
  },
  "status": "active",
  "created_at": 1640995200000,
  "updated_at": 1640995200000
}
```

---

## 📊 **Analytics Collection**

### **Structure: `/analytics/`**
```json
{
  "global": {
    "users": {
      "total": 10000,
      "active_monthly": 8500,
      "active_daily": 3200,
      "new_today": 45,
      "new_this_month": 1200,
      "by_subscription": {
        "free": 7000,
        "premium": 3000
      },
      "by_country": {
        "EG": 4000,
        "US": 2000,
        "UK": 1500
      }
    },
    "servers": {
      "total": 50,
      "active": 48,
      "maintenance": 2,
      "average_load": 35.5,
      "total_bandwidth_gbps": 500,
      "used_bandwidth_gbps": 177.5
    },
    "traffic": {
      "total_sessions_today": 15000,
      "total_data_gb_today": 2500,
      "average_session_duration": 3600,
      "peak_concurrent_users": 5000,
      "peak_time": "20:00-22:00 UTC"
    }
  },
  "daily": {
    "2024-01-01": {
      "users": {
        "active": 3200,
        "new": 45,
        "sessions": 15000
      },
      "traffic": {
        "total_gb": 2500,
        "peak_concurrent": 5000,
        "average_duration": 3600
      },
      "servers": {
        "average_load": 35.5,
        "incidents": 0,
        "maintenance_hours": 2
      }
    }
  },
  "monthly": {
    "2024-01": {
      "users": {
        "active": 8500,
        "new": 1200,
        "churned": 200
      },
      "revenue": {
        "total": 85000,
        "new_subscriptions": 12000,
        "renewals": 73000
      },
      "traffic": {
        "total_gb": 75000,
        "sessions": 450000,
        "average_duration": 3600
      }
    }
  }
}
```

---

## ⚙️ **System Collection**

### **Structure: `/system/`**
```json
{
  "config": {
    "app_version": "1.0.0",
    "min_supported_version": "0.9.0",
    "maintenance_mode": false,
    "registration_enabled": true,
    "free_tier_enabled": true,
    "max_free_users": 5000,
    "default_subscription": "free",
    "supported_protocols": ["vmess", "vless", "trojan", "shadowsocks"],
    "supported_languages": ["ar", "en"],
    "default_language": "ar"
  },
  "features": {
    "user_registration": true,
    "social_login": false,
    "referral_program": true,
    "affiliate_program": false,
    "multi_device": true,
    "kill_switch": true,
    "split_tunneling": true,
    "ad_blocking": true
  },
  "limits": {
    "max_devices_per_user": 5,
    "max_sessions_per_user": 3,
    "max_bandwidth_per_user_mbps": 100,
    "session_timeout_minutes": 1440,
    "idle_timeout_minutes": 30
  },
  "notifications": {
    "maintenance_alerts": true,
    "security_updates": true,
    "feature_announcements": true,
    "promotional_offers": false
  }
}
```

---

## 👑 **Admin Collection**

### **Structure: `/admin/`**
```json
{
  "users": {
    "admin_uid_1": {
      "uid": "admin_uid_1",
      "email": "<EMAIL>",
      "role": "super_admin",
      "permissions": ["*"],
      "created_at": 1640995200000,
      "last_login": 1640995200000,
      "login_count": 150
    }
  },
  "logs": {
    "log_id_1": {
      "id": "log_id_1",
      "type": "user_action",
      "action": "server_assigned",
      "admin_uid": "admin_uid_1",
      "target_uid": "user_uid_1",
      "details": {
        "server_id": "server_id_1",
        "reason": "subscription_upgrade"
      },
      "timestamp": 1640995200000,
      "ip_address": "***********"
    }
  },
  "statistics": {
    "dashboard": {
      "total_users": 10000,
      "active_users": 8500,
      "total_servers": 50,
      "active_servers": 48,
      "total_revenue": 85000,
      "monthly_growth": 15.5,
      "last_updated": 1640995200000
    }
  }
}
```

---

## 💾 **Backups Collection**

### **Structure: `/backups/`**
```json
{
  "backup_id_1": {
    "id": "backup_id_1",
    "type": "full",
    "status": "completed",
    "size_mb": 1024,
    "collections": ["users", "servers", "subscriptions"],
    "created_at": 1640995200000,
    "created_by": "system",
    "storage_url": "gs://bucket/backups/backup_id_1.json",
    "retention_days": 30,
    "expires_at": 1643587200000
  }
}
```

---

## 🔗 **Relationships and Indexes**

### **Key Relationships**
- **Users ↔ Servers**: Many-to-many through user.servers
- **Users ↔ Subscriptions**: One-to-one through user.subscription
- **Users ↔ Sessions**: One-to-many through user.stats.sessions
- **Admins ↔ Users**: One-to-many through admin actions
- **Servers ↔ Analytics**: One-to-many through usage tracking

### **Required Indexes**
```json
{
  "users": {
    ".indexOn": ["email", "role.type", "subscription.status", "created_at"]
  },
  "servers": {
    ".indexOn": ["status", "type", "location.country", "performance.load_percentage"]
  },
  "analytics/daily": {
    ".indexOn": [".key"]
  },
  "admin/logs": {
    ".indexOn": ["type", "admin_uid", "timestamp"]
  }
}
```

This comprehensive database schema provides a solid foundation for the V2Hoor VPN application with proper data organization, relationships, and scalability considerations.
