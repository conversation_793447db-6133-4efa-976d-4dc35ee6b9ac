package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdminUsersBinding
import com.mohamedrady.v2hoor.dto.UserProfile
import com.mohamedrady.v2hoor.service.AdminPermissionService
import com.mohamedrady.v2hoor.service.AdminManagementService
import com.mohamedrady.v2hoor.ui.adapter.AdminUsersAdapter
import com.mohamedrady.v2hoor.viewmodel.AdminUsersViewModel
import kotlinx.coroutines.launch

/**
 * Activity for managing users (Admin only)
 */
class AdminUsersActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdminUsersBinding
    private val viewModel: AdminUsersViewModel by viewModels()
    private lateinit var usersAdapter: AdminUsersAdapter
    private val adminService = AdminPermissionService.getInstance()
    private lateinit var adminManagementService: AdminManagementService

    override fun onCreate(savedInstanceState: Bundle?) {
        // Set theme before calling super.onCreate()
        setTheme(R.style.AppThemeMaterial_NoActionBar)
        super.onCreate(savedInstanceState)

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "🚀 Starting AdminUsersActivity onCreate")

        try {
            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "📱 Inflating layout")
            binding = ActivityAdminUsersBinding.inflate(layoutInflater)
            setContentView(binding.root)

            // Initialize admin management service
            adminManagementService = AdminManagementService.getInstance(this)

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "🎨 Setting up toolbar")
            setupToolbar()

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "🔐 Checking admin permissions")
            checkAdminPermissions()

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "✅ AdminUsersActivity onCreate completed successfully")
        } catch (e: Exception) {
            com.mohamedrady.v2hoor.util.CrashHandler.logError("AdminUsers", "💥 Error in AdminUsersActivity onCreate", e)
            Toast.makeText(this, "خطأ في تحميل صفحة إدارة المستخدمين", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun checkAdminPermissions() {
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "🔐 Starting permission check")

        lifecycleScope.launch {
            try {
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "👤 Checking if user is admin")
                val isAdmin = adminService.isAdmin()
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "🔍 Admin check result: $isAdmin")

                if (!isAdmin) {
                    com.mohamedrady.v2hoor.util.CrashHandler.logWarning("AdminUsers", "🚫 Access denied - user is not admin")
                    Toast.makeText(this@AdminUsersActivity, "غير مصرح لك بالوصول لهذه الصفحة", Toast.LENGTH_SHORT).show()
                    finish()
                    return@launch
                }

                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "✅ Admin access granted, initializing UI")

                // Initialize UI after permission check
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "📋 Setting up RecyclerView")
                setupRecyclerView()

                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "👀 Setting up observers")
                setupObservers()

                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "🖱️ Setting up click listeners")
                setupClickListeners()

                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "📊 Loading users data")
                loadAdminUsers()
                viewModel.loadUsers()

                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminUsers", "🎉 AdminUsersActivity fully initialized")
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("AdminUsers", "💥 Error checking admin permissions", e)
                Toast.makeText(this@AdminUsersActivity, "خطأ في التحقق من الصلاحيات", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "إدارة المستخدمين"
        }
    }

    private fun setupRecyclerView() {
        usersAdapter = AdminUsersAdapter(
            onUserClick = { user -> showUserDetails(user) },
            onToggleUserStatus = { user -> toggleUserStatus(user) },
            onDeleteUser = { user -> confirmDeleteUser(user) },
            onManageServers = { user -> manageUserServers(user) }
        )

        binding.recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(this@AdminUsersActivity)
            adapter = usersAdapter
        }
    }

    private fun setupObservers() {
        // Observe admin management service
        adminManagementService.allUsers.observe(this) { users ->
            usersAdapter.submitList(users.map { it.toUserProfile() })
            binding.textViewUsersCount.text = "إجمالي المستخدمين: ${users.size}"
        }

        adminManagementService.adminStats.observe(this) { stats ->
            updateAdminStats(stats)
        }

        // Keep existing ViewModel observers for compatibility
        viewModel.users.observe(this) { users ->
            usersAdapter.submitList(users)
            binding.textViewUsersCount.text = "إجمالي المستخدمين: ${users.size}"
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading)
                android.view.View.VISIBLE else android.view.View.GONE
        }

        viewModel.error.observe(this) { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }

        viewModel.statistics.observe(this) { stats ->
            stats?.let { updateStatistics(it) }
        }
    }

    private fun setupClickListeners() {
        binding.fabAddUser.setOnClickListener {
            addNewUser()
        }

        binding.buttonRefresh.setOnClickListener {
            viewModel.loadUsers()
        }

        binding.editTextSearch.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                val query = s?.toString() ?: ""
                if (query.isBlank()) {
                    viewModel.loadUsers()
                } else {
                    viewModel.searchUsers(query)
                }
            }
        })
    }

    private fun updateStatistics(stats: Map<String, Any>) {
        binding.apply {
            textViewTotalUsers.text = "${stats["totalUsers"]}"
            textViewActiveUsers.text = "${stats["activeUsers"]}"
            textViewInactiveUsers.text = "${stats["inactiveUsers"]}"
            textViewPremiumUsers.text = "${stats["premiumUsers"]}"
        }
    }

    private fun showUserDetails(user: UserProfile) {
        val intent = Intent(this, UserDetailsActivity::class.java).apply {
            putExtra("user_profile", user)
        }
        startActivity(intent)
    }

    private fun toggleUserStatus(user: UserProfile) {
        val newStatus = !user.isActive
        val message = if (newStatus) "تفعيل" else "إلغاء تفعيل"
        
        AlertDialog.Builder(this)
            .setTitle("تأكيد العملية")
            .setMessage("هل تريد $message المستخدم ${user.getFormattedDisplayName()}؟")
            .setPositiveButton("نعم") { _, _ ->
                viewModel.updateUserStatus(user.uid, newStatus)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun confirmDeleteUser(user: UserProfile) {
        lifecycleScope.launch {
            if (!adminService.isSuperAdmin()) {
                Toast.makeText(this@AdminUsersActivity, "فقط المدير العام يمكنه حذف المستخدمين", Toast.LENGTH_SHORT).show()
                return@launch
            }
        }

        AlertDialog.Builder(this)
            .setTitle("تحذير")
            .setMessage("هل تريد حذف المستخدم ${user.getFormattedDisplayName()} نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!")
            .setPositiveButton("حذف") { _, _ ->
                viewModel.deleteUser(user.uid)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun manageUserServers(user: UserProfile) {
        val intent = Intent(this, UserServersActivity::class.java).apply {
            putExtra("user_id", user.uid)
            putExtra("user_name", user.getFormattedDisplayName())
        }
        startActivity(intent)
    }

    private fun addNewUser() {
        val intent = Intent(this, AddUserActivity::class.java)
        startActivity(intent)
    }

    private fun showFilterDialog() {
        // TODO: Implement filter dialog
        Toast.makeText(this, "تصفية المستخدمين - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun exportUsers() {
        // TODO: Export users data
        Toast.makeText(this, "تصدير المستخدمين - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun importUsers() {
        // TODO: Import users data
        Toast.makeText(this, "استيراد المستخدمين - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun showBulkOperationsDialog() {
        // TODO: Implement bulk operations
        Toast.makeText(this, "العمليات الجماعية - قريباً", Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_admin_users, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_add_user -> {
                addNewUser()
                true
            }
            R.id.action_filter -> {
                showFilterDialog()
                true
            }
            R.id.action_export_users -> {
                exportUsers()
                true
            }
            R.id.action_import_users -> {
                importUsers()
                true
            }
            R.id.action_bulk_operations -> {
                showBulkOperationsDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showStatisticsDialog() {
        viewModel.statistics.value?.let { stats ->
            val message = """
                إحصائيات المستخدمين:
                
                إجمالي المستخدمين: ${stats["totalUsers"]}
                المستخدمين النشطين: ${stats["activeUsers"]}
                المستخدمين غير النشطين: ${stats["inactiveUsers"]}
                المستخدمين المميزين: ${stats["premiumUsers"]}
                المستخدمين المجانيين: ${stats["freeUsers"]}
            """.trimIndent()

            AlertDialog.Builder(this)
                .setTitle("إحصائيات المستخدمين")
                .setMessage(message)
                .setPositiveButton("موافق", null)
                .show()
        }
    }

    /**
     * Load users using admin management service
     */
    private fun loadAdminUsers() {
        lifecycleScope.launch {
            try {
                val result = adminManagementService.loadAllUsers()
                if (result.isFailure) {
                    val error = result.exceptionOrNull()
                    com.mohamedrady.v2hoor.util.CrashHandler.logError("AdminUsers", "Failed to load admin users", error)
                    Toast.makeText(this@AdminUsersActivity, "فشل في تحميل المستخدمين: ${error?.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("AdminUsers", "Error loading admin users", e)
                Toast.makeText(this@AdminUsersActivity, "خطأ في تحميل المستخدمين", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * Update admin statistics display
     */
    private fun updateAdminStats(stats: AdminManagementService.AdminStats) {
        binding.apply {
            textViewTotalUsers.text = stats.totalUsers.toString()
            textViewActiveUsers.text = stats.activeUsers.toString()

            // Find expired users TextView if it exists
            try {
                val expiredUsersView = findViewById<android.widget.TextView>(R.id.textViewExpiredUsers)
                expiredUsersView?.text = stats.expiredUsers.toString()
            } catch (e: Exception) {
                // TextView doesn't exist, ignore
            }

            // Find admin users TextView if it exists
            try {
                val adminUsersView = findViewById<android.widget.TextView>(R.id.textViewAdminUsers)
                adminUsersView?.text = stats.adminUsers.toString()
            } catch (e: Exception) {
                // TextView doesn't exist, ignore
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh data when returning to activity
        loadAdminUsers()
        viewModel.loadUsers()
    }
}
