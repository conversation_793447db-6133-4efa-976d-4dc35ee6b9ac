// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SubscriptionStatusBannerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnCloseBanner;

  @NonNull
  public final Button btnSubscriptionAction;

  @NonNull
  public final ImageView ivSubscriptionIcon;

  @NonNull
  public final LinearLayout layoutDataUsage;

  @NonNull
  public final ProgressBar progressDataUsage;

  @NonNull
  public final LinearLayout subscriptionBanner;

  @NonNull
  public final TextView tvDataUsage;

  @NonNull
  public final TextView tvSubscriptionDetails;

  @NonNull
  public final TextView tvSubscriptionStatus;

  private SubscriptionStatusBannerBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnCloseBanner, @NonNull Button btnSubscriptionAction,
      @NonNull ImageView ivSubscriptionIcon, @NonNull LinearLayout layoutDataUsage,
      @NonNull ProgressBar progressDataUsage, @NonNull LinearLayout subscriptionBanner,
      @NonNull TextView tvDataUsage, @NonNull TextView tvSubscriptionDetails,
      @NonNull TextView tvSubscriptionStatus) {
    this.rootView = rootView;
    this.btnCloseBanner = btnCloseBanner;
    this.btnSubscriptionAction = btnSubscriptionAction;
    this.ivSubscriptionIcon = ivSubscriptionIcon;
    this.layoutDataUsage = layoutDataUsage;
    this.progressDataUsage = progressDataUsage;
    this.subscriptionBanner = subscriptionBanner;
    this.tvDataUsage = tvDataUsage;
    this.tvSubscriptionDetails = tvSubscriptionDetails;
    this.tvSubscriptionStatus = tvSubscriptionStatus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SubscriptionStatusBannerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SubscriptionStatusBannerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.subscription_status_banner, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SubscriptionStatusBannerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_close_banner;
      ImageButton btnCloseBanner = ViewBindings.findChildViewById(rootView, id);
      if (btnCloseBanner == null) {
        break missingId;
      }

      id = R.id.btn_subscription_action;
      Button btnSubscriptionAction = ViewBindings.findChildViewById(rootView, id);
      if (btnSubscriptionAction == null) {
        break missingId;
      }

      id = R.id.iv_subscription_icon;
      ImageView ivSubscriptionIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivSubscriptionIcon == null) {
        break missingId;
      }

      id = R.id.layout_data_usage;
      LinearLayout layoutDataUsage = ViewBindings.findChildViewById(rootView, id);
      if (layoutDataUsage == null) {
        break missingId;
      }

      id = R.id.progress_data_usage;
      ProgressBar progressDataUsage = ViewBindings.findChildViewById(rootView, id);
      if (progressDataUsage == null) {
        break missingId;
      }

      LinearLayout subscriptionBanner = (LinearLayout) rootView;

      id = R.id.tv_data_usage;
      TextView tvDataUsage = ViewBindings.findChildViewById(rootView, id);
      if (tvDataUsage == null) {
        break missingId;
      }

      id = R.id.tv_subscription_details;
      TextView tvSubscriptionDetails = ViewBindings.findChildViewById(rootView, id);
      if (tvSubscriptionDetails == null) {
        break missingId;
      }

      id = R.id.tv_subscription_status;
      TextView tvSubscriptionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvSubscriptionStatus == null) {
        break missingId;
      }

      return new SubscriptionStatusBannerBinding((LinearLayout) rootView, btnCloseBanner,
          btnSubscriptionAction, ivSubscriptionIcon, layoutDataUsage, progressDataUsage,
          subscriptionBanner, tvDataUsage, tvSubscriptionDetails, tvSubscriptionStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
