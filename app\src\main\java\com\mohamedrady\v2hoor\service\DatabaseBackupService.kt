package com.mohamedrady.v2hoor.service

import android.content.Context
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Database Backup and Restore Service
 * Handles automated backups, manual backups, and data restoration
 */
class DatabaseBackupService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: DatabaseBackupService? = null
        
        fun getInstance(context: Context): DatabaseBackupService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DatabaseBackupService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val BACKUP_DIR = "database_backups"
        private const val MAX_LOCAL_BACKUPS = 10
        private const val BACKUP_RETENTION_DAYS = 30
    }
    
    enum class BackupType {
        FULL, INCREMENTAL, USER_DATA, SYSTEM_CONFIG, SERVERS_ONLY
    }
    
    enum class BackupStatus {
        PENDING, IN_PROGRESS, COMPLETED, FAILED, EXPIRED
    }
    
    data class BackupInfo(
        val id: String,
        val type: BackupType,
        val status: BackupStatus,
        val createdAt: Long,
        val size: Long,
        val collections: List<String>,
        val filePath: String? = null,
        val cloudUrl: String? = null,
        val checksum: String? = null
    )
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    
    /**
     * Create a full database backup
     */
    suspend fun createFullBackup(): Result<BackupInfo> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "💾 Starting full database backup...")
                
                val backupId = generateBackupId("full")
                val backupInfo = BackupInfo(
                    id = backupId,
                    type = BackupType.FULL,
                    status = BackupStatus.IN_PROGRESS,
                    createdAt = System.currentTimeMillis(),
                    size = 0,
                    collections = listOf("users", "servers", "subscriptions", "analytics", "system", "admin")
                )
                
                // Log backup start
                logBackupStart(backupInfo)
                
                // Export all data
                val backupData = exportAllData()
                
                // Save to local file
                val localFile = saveBackupToFile(backupId, backupData)
                val fileSize = localFile.length()
                
                // Calculate checksum
                val checksum = calculateChecksum(backupData)
                
                // Update backup info
                val completedBackup = backupInfo.copy(
                    status = BackupStatus.COMPLETED,
                    size = fileSize,
                    filePath = localFile.absolutePath,
                    checksum = checksum
                )
                
                // Log backup completion
                logBackupCompletion(completedBackup)
                
                // Clean up old backups
                cleanupOldBackups()
                
                android.util.Log.i(AppConfig.TAG, "✅ Full backup completed: $backupId (${formatFileSize(fileSize)})")
                Result.success(completedBackup)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Full backup failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Create incremental backup (only changed data)
     */
    suspend fun createIncrementalBackup(lastBackupTime: Long): Result<BackupInfo> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "💾 Starting incremental backup since ${Date(lastBackupTime)}")
                
                val backupId = generateBackupId("incremental")
                val backupInfo = BackupInfo(
                    id = backupId,
                    type = BackupType.INCREMENTAL,
                    status = BackupStatus.IN_PROGRESS,
                    createdAt = System.currentTimeMillis(),
                    size = 0,
                    collections = listOf("users", "servers", "analytics")
                )
                
                // Log backup start
                logBackupStart(backupInfo)
                
                // Export only changed data
                val backupData = exportIncrementalData(lastBackupTime)
                
                // Save to local file
                val localFile = saveBackupToFile(backupId, backupData)
                val fileSize = localFile.length()
                
                // Calculate checksum
                val checksum = calculateChecksum(backupData)
                
                // Update backup info
                val completedBackup = backupInfo.copy(
                    status = BackupStatus.COMPLETED,
                    size = fileSize,
                    filePath = localFile.absolutePath,
                    checksum = checksum
                )
                
                // Log backup completion
                logBackupCompletion(completedBackup)
                
                android.util.Log.i(AppConfig.TAG, "✅ Incremental backup completed: $backupId (${formatFileSize(fileSize)})")
                Result.success(completedBackup)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Incremental backup failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Create user data backup
     */
    suspend fun createUserDataBackup(userId: String): Result<BackupInfo> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "💾 Starting user data backup for: $userId")
                
                val backupId = generateBackupId("user_$userId")
                val backupInfo = BackupInfo(
                    id = backupId,
                    type = BackupType.USER_DATA,
                    status = BackupStatus.IN_PROGRESS,
                    createdAt = System.currentTimeMillis(),
                    size = 0,
                    collections = listOf("users/$userId")
                )
                
                // Export user data
                val userData = exportUserData(userId)
                
                // Save to local file
                val localFile = saveBackupToFile(backupId, userData)
                val fileSize = localFile.length()
                
                // Calculate checksum
                val checksum = calculateChecksum(userData)
                
                // Update backup info
                val completedBackup = backupInfo.copy(
                    status = BackupStatus.COMPLETED,
                    size = fileSize,
                    filePath = localFile.absolutePath,
                    checksum = checksum
                )
                
                android.util.Log.i(AppConfig.TAG, "✅ User data backup completed: $backupId")
                Result.success(completedBackup)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ User data backup failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Restore from backup
     */
    suspend fun restoreFromBackup(backupInfo: BackupInfo): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "🔄 Starting restore from backup: ${backupInfo.id}")
                
                // Verify backup file exists and is valid
                val backupFile = File(backupInfo.filePath ?: throw IllegalArgumentException("Backup file path is null"))
                if (!backupFile.exists()) {
                    throw IllegalStateException("Backup file not found: ${backupFile.absolutePath}")
                }
                
                // Read backup data
                val backupData = backupFile.readText()
                val jsonData = JSONObject(backupData)
                
                // Verify checksum if available
                if (backupInfo.checksum != null) {
                    val currentChecksum = calculateChecksum(backupData)
                    if (currentChecksum != backupInfo.checksum) {
                        throw IllegalStateException("Backup checksum mismatch - file may be corrupted")
                    }
                }
                
                // Create restore point before proceeding
                val restorePointBackup = createFullBackup()
                if (restorePointBackup.isFailure) {
                    throw IllegalStateException("Failed to create restore point backup")
                }
                
                // Restore data based on backup type
                when (backupInfo.type) {
                    BackupType.FULL -> restoreFullData(jsonData)
                    BackupType.USER_DATA -> restoreUserData(jsonData)
                    BackupType.SYSTEM_CONFIG -> restoreSystemConfig(jsonData)
                    BackupType.SERVERS_ONLY -> restoreServersData(jsonData)
                    BackupType.INCREMENTAL -> restoreIncrementalData(jsonData)
                }
                
                // Log restore completion
                logRestoreCompletion(backupInfo)
                
                android.util.Log.i(AppConfig.TAG, "✅ Restore completed successfully from backup: ${backupInfo.id}")
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Restore failed from backup: ${backupInfo.id}", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Export all database data
     */
    private suspend fun exportAllData(): String {
        val allData = mutableMapOf<String, Any?>()
        
        val collections = listOf("users", "servers", "subscriptions", "analytics", "system", "admin")
        
        for (collection in collections) {
            try {
                val data = exportCollection(collection)
                allData[collection] = data
            } catch (e: Exception) {
                android.util.Log.w(AppConfig.TAG, "Failed to export collection: $collection", e)
                allData[collection] = null
            }
        }
        
        allData["backup_metadata"] = mapOf(
            "created_at" to System.currentTimeMillis(),
            "app_version" to "1.0.0",
            "backup_version" to "1.0",
            "collections" to collections
        )
        
        return JSONObject(allData).toString(2)
    }
    
    /**
     * Export incremental data (changed since last backup)
     */
    private suspend fun exportIncrementalData(lastBackupTime: Long): String {
        val incrementalData = mutableMapOf<String, Any?>()
        
        // For simplicity, we'll export recent user activity and analytics
        // In a real implementation, you'd track changes more precisely
        
        incrementalData["users_updated"] = exportRecentUserUpdates(lastBackupTime)
        incrementalData["analytics_new"] = exportRecentAnalytics(lastBackupTime)
        
        incrementalData["backup_metadata"] = mapOf(
            "created_at" to System.currentTimeMillis(),
            "backup_type" to "incremental",
            "since_timestamp" to lastBackupTime
        )
        
        return JSONObject(incrementalData).toString(2)
    }
    
    /**
     * Export single collection data
     */
    private suspend fun exportCollection(collection: String): Any? {
        return suspendCancellableCoroutine { continuation ->
            database.child(collection).addListenerForSingleValueEvent(object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    continuation.resume(snapshot.value)
                }
                
                override fun onCancelled(error: DatabaseError) {
                    continuation.resumeWithException(error.toException())
                }
            })
        }
    }
    
    /**
     * Export user data
     */
    private suspend fun exportUserData(userId: String): String {
        val userData = exportCollection("users/$userId")
        
        val userBackup = mapOf(
            "user_data" to userData,
            "backup_metadata" to mapOf(
                "user_id" to userId,
                "created_at" to System.currentTimeMillis(),
                "backup_type" to "user_data"
            )
        )
        
        return JSONObject(userBackup).toString(2)
    }
    
    /**
     * Export recent user updates
     */
    private suspend fun exportRecentUserUpdates(since: Long): Any? {
        // This would query users updated since the timestamp
        // For now, return null as placeholder
        return null
    }
    
    /**
     * Export recent analytics
     */
    private suspend fun exportRecentAnalytics(since: Long): Any? {
        // This would query analytics data since the timestamp
        // For now, return null as placeholder
        return null
    }
    
    /**
     * Save backup data to file
     */
    private fun saveBackupToFile(backupId: String, data: String): File {
        val backupDir = File(context.filesDir, BACKUP_DIR)
        if (!backupDir.exists()) {
            backupDir.mkdirs()
        }
        
        val backupFile = File(backupDir, "$backupId.json")
        FileWriter(backupFile).use { writer ->
            writer.write(data)
        }
        
        return backupFile
    }
    
    /**
     * Calculate data checksum
     */
    private fun calculateChecksum(data: String): String {
        return try {
            val digest = java.security.MessageDigest.getInstance("SHA-256")
            val hash = digest.digest(data.toByteArray())
            hash.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Restore full data
     */
    private suspend fun restoreFullData(jsonData: JSONObject) {
        val collections = listOf("users", "servers", "subscriptions", "analytics", "system", "admin")
        
        for (collection in collections) {
            if (jsonData.has(collection)) {
                val collectionData = jsonData.get(collection)
                if (collectionData != null && collectionData != JSONObject.NULL) {
                    restoreCollection(collection, collectionData)
                }
            }
        }
    }
    
    /**
     * Restore user data
     */
    private suspend fun restoreUserData(jsonData: JSONObject) {
        if (jsonData.has("user_data")) {
            val userData = jsonData.get("user_data")
            val metadata = jsonData.getJSONObject("backup_metadata")
            val userId = metadata.getString("user_id")
            
            restoreCollection("users/$userId", userData)
        }
    }
    
    /**
     * Restore system config
     */
    private suspend fun restoreSystemConfig(jsonData: JSONObject) {
        if (jsonData.has("system")) {
            restoreCollection("system", jsonData.get("system"))
        }
    }
    
    /**
     * Restore servers data
     */
    private suspend fun restoreServersData(jsonData: JSONObject) {
        if (jsonData.has("servers")) {
            restoreCollection("servers", jsonData.get("servers"))
        }
    }
    
    /**
     * Restore incremental data
     */
    private suspend fun restoreIncrementalData(jsonData: JSONObject) {
        // Restore incremental changes
        // Implementation depends on the specific incremental backup format
    }
    
    /**
     * Restore collection data
     */
    private suspend fun restoreCollection(path: String, data: Any) {
        suspendCancellableCoroutine<Unit> { continuation ->
            database.child(path).setValue(data)
                .addOnSuccessListener {
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Generate backup ID
     */
    private fun generateBackupId(type: String): String {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return "backup_${type}_${timestamp}_${UUID.randomUUID().toString().take(8)}"
    }
    
    /**
     * Log backup start
     */
    private suspend fun logBackupStart(backupInfo: BackupInfo) {
        try {
            val logData = mapOf(
                "backup_id" to backupInfo.id,
                "type" to backupInfo.type.name,
                "status" to "started",
                "started_at" to ServerValue.TIMESTAMP,
                "collections" to backupInfo.collections
            )
            
            database.child("backups").child(backupInfo.id).setValue(logData)
        } catch (e: Exception) {
            android.util.Log.w(AppConfig.TAG, "Failed to log backup start", e)
        }
    }
    
    /**
     * Log backup completion
     */
    private suspend fun logBackupCompletion(backupInfo: BackupInfo) {
        try {
            val updates = mapOf(
                "backups/${backupInfo.id}/status" to backupInfo.status.name,
                "backups/${backupInfo.id}/completed_at" to ServerValue.TIMESTAMP,
                "backups/${backupInfo.id}/size_bytes" to backupInfo.size,
                "backups/${backupInfo.id}/checksum" to backupInfo.checksum
            )
            
            database.updateChildren(updates)
        } catch (e: Exception) {
            android.util.Log.w(AppConfig.TAG, "Failed to log backup completion", e)
        }
    }
    
    /**
     * Log restore completion
     */
    private suspend fun logRestoreCompletion(backupInfo: BackupInfo) {
        try {
            val logData = mapOf(
                "backup_id" to backupInfo.id,
                "restored_at" to ServerValue.TIMESTAMP,
                "restored_by" to (auth.currentUser?.uid ?: "system")
            )
            
            database.child("system").child("restore_logs").push().setValue(logData)
        } catch (e: Exception) {
            android.util.Log.w(AppConfig.TAG, "Failed to log restore completion", e)
        }
    }
    
    /**
     * Clean up old backups
     */
    private fun cleanupOldBackups() {
        try {
            val backupDir = File(context.filesDir, BACKUP_DIR)
            if (!backupDir.exists()) return
            
            val backupFiles = backupDir.listFiles()?.sortedByDescending { it.lastModified() }
            
            // Keep only the most recent backups
            backupFiles?.drop(MAX_LOCAL_BACKUPS)?.forEach { file ->
                if (file.delete()) {
                    android.util.Log.d(AppConfig.TAG, "Deleted old backup: ${file.name}")
                }
            }
            
            // Delete backups older than retention period
            val cutoffTime = System.currentTimeMillis() - (BACKUP_RETENTION_DAYS * 24 * 60 * 60 * 1000L)
            backupFiles?.filter { it.lastModified() < cutoffTime }?.forEach { file ->
                if (file.delete()) {
                    android.util.Log.d(AppConfig.TAG, "Deleted expired backup: ${file.name}")
                }
            }
        } catch (e: Exception) {
            android.util.Log.w(AppConfig.TAG, "Failed to cleanup old backups", e)
        }
    }
    
    /**
     * Format file size for display
     */
    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 -> String.format("%.2f MB", bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> String.format("%.2f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }
    
    /**
     * Get list of available backups
     */
    fun getAvailableBackups(): List<BackupInfo> {
        val backups = mutableListOf<BackupInfo>()
        
        try {
            val backupDir = File(context.filesDir, BACKUP_DIR)
            if (!backupDir.exists()) return backups
            
            backupDir.listFiles()?.forEach { file ->
                if (file.name.endsWith(".json")) {
                    val backupInfo = BackupInfo(
                        id = file.nameWithoutExtension,
                        type = BackupType.FULL, // Default, would be parsed from filename
                        status = BackupStatus.COMPLETED,
                        createdAt = file.lastModified(),
                        size = file.length(),
                        collections = emptyList(),
                        filePath = file.absolutePath
                    )
                    backups.add(backupInfo)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to get available backups", e)
        }
        
        return backups.sortedByDescending { it.createdAt }
    }
}
