-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:320:9-331:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b67b39daef2398884ed6af353040af9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b67b39daef2398884ed6af353040af9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:324:13-31
	android:authorities
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:322:13-68
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:323:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:321:13-67
provider#androidx.core.content.FileProvider
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:333:9-341:20
	android:grantUriPermissions
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:337:13-47
	android:authorities
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:335:13-57
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:336:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:334:13-62
manifest
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-345:12
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-345:12
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-345:12
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-345:12
MERGED from [libv2ray.aar] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f374f69b28e65eadf3f70af924282c94\transformed\libv2ray\AndroidManifest.xml:1:1-2:50
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3336b61ae9afa4a678f0a7653ac4b5cc\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\881daab11df0f8fb6be0bbb2034fa98e\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e10290a8de075a2af92b4127d3fcd4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.GrenderG:Toasty:1.5.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3671adc343b59afebe99d3ec6265d6d7\transformed\Toasty-1.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [io.coil-kt.coil3:coil-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\10d6fca8482402d595a52ae030221f3f\transformed\coil-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-core-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d70395873c50a004c47266b87418baa0\transformed\coil-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\add60eebd82ed01c935569d6c4044712\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41b8add6822ef491a03e60c784031bf7\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65324aff12f16468e03fc5b512ae58fc\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\673b3fbf6902e1c8520e12f8db1be6e2\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2fe266abc614fe4a1a9f0ef8f5ae6b71\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f68ab7854e90816622d4e41b6bdf2b2c\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e9c4fffcc61ffb5e8382deeedde39b2\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\004c9aef0bfe597de4245d1fd9384d65\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\373a2c6263a5db113b840d962db507f3\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3301c563da9d289aac88c6ca3529e20c\transformed\recyclerview-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8717f4201504047d35ce4cf7188de2d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98f63c95ff93e2f4f65236a575c82fb6\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\43771e54e3821cd3b650031cb057c502\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2e043999371eba3d6a775c0ebb62492e\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d3c30427f5b0422d5189d139ca1b0f4\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5952be130a0125f3eebf22a58b8a1ede\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\58551c30a02dd338e8c26b3ad8d9086f\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5aa0256fab7585d02a507f59930e9f5d\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7969a17312b879cb64d733ab79c3405\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6bafbcd39a4eea3fe927ab325bdfa32\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\730b3a7b75a4d2b501c35ae3324dde6e\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74c1f35d072f78da87ca7fdefa9b8139\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5c1f58a58ca000404c5b280db793ba2\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c97973f8bb354c108315c4124d303231\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d26cbaf0897ee6e94d48b38b93b033e1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7dd538137fb70394e71275cdeade7fcc\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c85c27c97bce824d2f61500ea043919\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\722b6e60855306b97be5719d0047944f\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\43f80c422e65d725a8b34fc24966139b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\19bfc16374171ba7a26e435a1ff1fa3b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.blacksquircle.ui:editorkit:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d86c244618d206898636e89e121eaa6\transformed\editorkit-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c37b988265ba0d20b9b71b62dfcf149\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2cb3aa761a0d3622928aa1eb74643bf2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e48c933591dcf777bddcd43663c67f8\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fdb5184ebaa721167d76bea376ae4f0c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\838ea3d6fc4d96d4723833af8c799d13\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba00bb078231f50d815e7c2c79fbd77c\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6c52cf283cdb5b1442805381c5e8512\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06e842e9e2185b842934479bba4f5a07\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\670b7bd08374344b87a918111d10b3d1\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9f7e4162378aec24a148c1db35cb9e8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fac676b4e1c0ee2b6233dfa300c1ec0\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d6d3d10c0196bb94b18f8d136f9af96\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf16cb607471652bf24dee0156a5408d\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aaec5b52f0b87a27b87b977c567045d7\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74d0b04c668c9acb1f38e673a708d3e4\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c588be67285252bb290c6b2c2549687a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0a98dea105a8516fe1dc0ed52b73b65b\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\660815bffd32e4dfbff6f9601f6257a8\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0204a76ee878696f53b1084c3cc59506\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1edf75348394b83d0d150ec9b97e0af0\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98a82b51eba9767a751bff19f39c0c7d\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bc106441b4b2bc4ab092517f22ce024b\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9c57edc1f48c0988cedc6fb5a00e3ddf\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e4b5b378034c4c39d6cbe4a347b43e6\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61576a727fcbef70bc2df616733586e2\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7a606b5c54e3225ba8edb6d80b04c104\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d6db5ad63ee943d2489e6cf90674ff7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be0b7d272e566bfd154d1bf5d5ba0c43\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fbfe99676c41211a4bfd510557294d9b\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb2a86983ad6559594debb17aca21969\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09d681927bb72583b9573f64ab7165b4\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f4e52344bf6f6b127fd672bf03ca4a\transformed\flexbox-3.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e09aa481a2483f3d3bc09954c2742862\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1129f2180ae7f1a4156b3703a9bc7fa4\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8b581fa548d5d3a3d06de6595f1d6c2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d8d479e8ce08d49f7462057f5cd16bf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b67b39daef2398884ed6af353040af9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eaca7bdf83c884987da62c8de8b6124d\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5723ba2e425e6779c76114ddaa1fa7cc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c20594b0845838e4988518be8ee73c19\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4aa8a0a36e73dc5400a3d4dc9f1c290\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.tencent:mmkv-static:1.3.12] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d4167531ec0553e96d158f0d3d1381a\transformed\mmkv-static-1.3.12\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c445bd27e5eaa5dd7d8b92b5bf86d7c2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4c356e6d5aa46cb1757d1f53c8ca8b17\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\97daf22df7e5c6e3b5afccf9cf12b867\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca11ba64caf1f4453572300ccfced8ed\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c5cf3686650254f44f5144222a5dff2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\11ee075c9248c74ab1545c6a17bf994e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e674c2c08b74154f7b30298e327906\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b512378e1c9c32ba5078cbb1f9a8e44c\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d7d90daf170696a22219bfcc22e0cd9\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68f8db486281b0ce9473a27dc665756f\transformed\grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\440ca6e3fd3df4d712d302f721315852\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\462d21a29d239b5d9a5b25fd8c51059a\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:11-69
	tools:ignore
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:4:5-43
supports-screens
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
	android:largeScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
	android:smallScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
	android:normalScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
	android:xlargeScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
	android:anyDensity
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
uses-sdk
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
MERGED from [libv2ray.aar] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f374f69b28e65eadf3f70af924282c94\transformed\libv2ray\AndroidManifest.xml:2:1-39
MERGED from [libv2ray.aar] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f374f69b28e65eadf3f70af924282c94\transformed\libv2ray\AndroidManifest.xml:2:1-39
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:6:5-44
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3336b61ae9afa4a678f0a7653ac4b5cc\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3336b61ae9afa4a678f0a7653ac4b5cc\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\881daab11df0f8fb6be0bbb2034fa98e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\881daab11df0f8fb6be0bbb2034fa98e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e10290a8de075a2af92b4127d3fcd4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e10290a8de075a2af92b4127d3fcd4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.GrenderG:Toasty:1.5.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3671adc343b59afebe99d3ec6265d6d7\transformed\Toasty-1.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.GrenderG:Toasty:1.5.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3671adc343b59afebe99d3ec6265d6d7\transformed\Toasty-1.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.coil-kt.coil3:coil-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\10d6fca8482402d595a52ae030221f3f\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\10d6fca8482402d595a52ae030221f3f\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d70395873c50a004c47266b87418baa0\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d70395873c50a004c47266b87418baa0\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\add60eebd82ed01c935569d6c4044712\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\add60eebd82ed01c935569d6c4044712\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41b8add6822ef491a03e60c784031bf7\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41b8add6822ef491a03e60c784031bf7\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65324aff12f16468e03fc5b512ae58fc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65324aff12f16468e03fc5b512ae58fc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\673b3fbf6902e1c8520e12f8db1be6e2\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\673b3fbf6902e1c8520e12f8db1be6e2\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2fe266abc614fe4a1a9f0ef8f5ae6b71\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2fe266abc614fe4a1a9f0ef8f5ae6b71\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f68ab7854e90816622d4e41b6bdf2b2c\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f68ab7854e90816622d4e41b6bdf2b2c\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e9c4fffcc61ffb5e8382deeedde39b2\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e9c4fffcc61ffb5e8382deeedde39b2\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\004c9aef0bfe597de4245d1fd9384d65\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\004c9aef0bfe597de4245d1fd9384d65\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\373a2c6263a5db113b840d962db507f3\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\373a2c6263a5db113b840d962db507f3\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3301c563da9d289aac88c6ca3529e20c\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3301c563da9d289aac88c6ca3529e20c\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8717f4201504047d35ce4cf7188de2d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8717f4201504047d35ce4cf7188de2d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98f63c95ff93e2f4f65236a575c82fb6\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98f63c95ff93e2f4f65236a575c82fb6\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\43771e54e3821cd3b650031cb057c502\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\43771e54e3821cd3b650031cb057c502\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2e043999371eba3d6a775c0ebb62492e\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2e043999371eba3d6a775c0ebb62492e\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d3c30427f5b0422d5189d139ca1b0f4\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d3c30427f5b0422d5189d139ca1b0f4\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5952be130a0125f3eebf22a58b8a1ede\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5952be130a0125f3eebf22a58b8a1ede\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\58551c30a02dd338e8c26b3ad8d9086f\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\58551c30a02dd338e8c26b3ad8d9086f\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5aa0256fab7585d02a507f59930e9f5d\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5aa0256fab7585d02a507f59930e9f5d\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7969a17312b879cb64d733ab79c3405\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7969a17312b879cb64d733ab79c3405\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6bafbcd39a4eea3fe927ab325bdfa32\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6bafbcd39a4eea3fe927ab325bdfa32\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\730b3a7b75a4d2b501c35ae3324dde6e\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\730b3a7b75a4d2b501c35ae3324dde6e\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74c1f35d072f78da87ca7fdefa9b8139\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74c1f35d072f78da87ca7fdefa9b8139\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5c1f58a58ca000404c5b280db793ba2\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5c1f58a58ca000404c5b280db793ba2\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c97973f8bb354c108315c4124d303231\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c97973f8bb354c108315c4124d303231\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d26cbaf0897ee6e94d48b38b93b033e1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d26cbaf0897ee6e94d48b38b93b033e1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7dd538137fb70394e71275cdeade7fcc\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7dd538137fb70394e71275cdeade7fcc\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c85c27c97bce824d2f61500ea043919\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c85c27c97bce824d2f61500ea043919\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\722b6e60855306b97be5719d0047944f\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\722b6e60855306b97be5719d0047944f\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\43f80c422e65d725a8b34fc24966139b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\43f80c422e65d725a8b34fc24966139b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\19bfc16374171ba7a26e435a1ff1fa3b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\19bfc16374171ba7a26e435a1ff1fa3b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.blacksquircle.ui:editorkit:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d86c244618d206898636e89e121eaa6\transformed\editorkit-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.blacksquircle.ui:editorkit:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d86c244618d206898636e89e121eaa6\transformed\editorkit-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c37b988265ba0d20b9b71b62dfcf149\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c37b988265ba0d20b9b71b62dfcf149\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2cb3aa761a0d3622928aa1eb74643bf2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2cb3aa761a0d3622928aa1eb74643bf2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e48c933591dcf777bddcd43663c67f8\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e48c933591dcf777bddcd43663c67f8\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fdb5184ebaa721167d76bea376ae4f0c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fdb5184ebaa721167d76bea376ae4f0c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\838ea3d6fc4d96d4723833af8c799d13\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\838ea3d6fc4d96d4723833af8c799d13\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba00bb078231f50d815e7c2c79fbd77c\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba00bb078231f50d815e7c2c79fbd77c\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6c52cf283cdb5b1442805381c5e8512\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6c52cf283cdb5b1442805381c5e8512\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06e842e9e2185b842934479bba4f5a07\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06e842e9e2185b842934479bba4f5a07\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\670b7bd08374344b87a918111d10b3d1\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\670b7bd08374344b87a918111d10b3d1\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9f7e4162378aec24a148c1db35cb9e8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9f7e4162378aec24a148c1db35cb9e8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fac676b4e1c0ee2b6233dfa300c1ec0\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fac676b4e1c0ee2b6233dfa300c1ec0\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d6d3d10c0196bb94b18f8d136f9af96\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d6d3d10c0196bb94b18f8d136f9af96\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf16cb607471652bf24dee0156a5408d\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf16cb607471652bf24dee0156a5408d\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aaec5b52f0b87a27b87b977c567045d7\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aaec5b52f0b87a27b87b977c567045d7\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74d0b04c668c9acb1f38e673a708d3e4\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74d0b04c668c9acb1f38e673a708d3e4\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c588be67285252bb290c6b2c2549687a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c588be67285252bb290c6b2c2549687a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0a98dea105a8516fe1dc0ed52b73b65b\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0a98dea105a8516fe1dc0ed52b73b65b\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\660815bffd32e4dfbff6f9601f6257a8\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\660815bffd32e4dfbff6f9601f6257a8\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0204a76ee878696f53b1084c3cc59506\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0204a76ee878696f53b1084c3cc59506\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1edf75348394b83d0d150ec9b97e0af0\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1edf75348394b83d0d150ec9b97e0af0\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98a82b51eba9767a751bff19f39c0c7d\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98a82b51eba9767a751bff19f39c0c7d\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bc106441b4b2bc4ab092517f22ce024b\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bc106441b4b2bc4ab092517f22ce024b\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9c57edc1f48c0988cedc6fb5a00e3ddf\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9c57edc1f48c0988cedc6fb5a00e3ddf\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e4b5b378034c4c39d6cbe4a347b43e6\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e4b5b378034c4c39d6cbe4a347b43e6\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61576a727fcbef70bc2df616733586e2\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\61576a727fcbef70bc2df616733586e2\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7a606b5c54e3225ba8edb6d80b04c104\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7a606b5c54e3225ba8edb6d80b04c104\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d6db5ad63ee943d2489e6cf90674ff7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d6db5ad63ee943d2489e6cf90674ff7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be0b7d272e566bfd154d1bf5d5ba0c43\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be0b7d272e566bfd154d1bf5d5ba0c43\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fbfe99676c41211a4bfd510557294d9b\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fbfe99676c41211a4bfd510557294d9b\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb2a86983ad6559594debb17aca21969\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cb2a86983ad6559594debb17aca21969\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09d681927bb72583b9573f64ab7165b4\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09d681927bb72583b9573f64ab7165b4\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f4e52344bf6f6b127fd672bf03ca4a\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f4e52344bf6f6b127fd672bf03ca4a\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e09aa481a2483f3d3bc09954c2742862\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e09aa481a2483f3d3bc09954c2742862\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1129f2180ae7f1a4156b3703a9bc7fa4\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1129f2180ae7f1a4156b3703a9bc7fa4\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8b581fa548d5d3a3d06de6595f1d6c2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8b581fa548d5d3a3d06de6595f1d6c2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d8d479e8ce08d49f7462057f5cd16bf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d8d479e8ce08d49f7462057f5cd16bf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b67b39daef2398884ed6af353040af9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b67b39daef2398884ed6af353040af9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eaca7bdf83c884987da62c8de8b6124d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eaca7bdf83c884987da62c8de8b6124d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5723ba2e425e6779c76114ddaa1fa7cc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5723ba2e425e6779c76114ddaa1fa7cc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c20594b0845838e4988518be8ee73c19\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c20594b0845838e4988518be8ee73c19\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4aa8a0a36e73dc5400a3d4dc9f1c290\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4aa8a0a36e73dc5400a3d4dc9f1c290\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.tencent:mmkv-static:1.3.12] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d4167531ec0553e96d158f0d3d1381a\transformed\mmkv-static-1.3.12\AndroidManifest.xml:5:5-7:41
MERGED from [com.tencent:mmkv-static:1.3.12] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d4167531ec0553e96d158f0d3d1381a\transformed\mmkv-static-1.3.12\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c445bd27e5eaa5dd7d8b92b5bf86d7c2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c445bd27e5eaa5dd7d8b92b5bf86d7c2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4c356e6d5aa46cb1757d1f53c8ca8b17\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4c356e6d5aa46cb1757d1f53c8ca8b17\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\97daf22df7e5c6e3b5afccf9cf12b867\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\97daf22df7e5c6e3b5afccf9cf12b867\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca11ba64caf1f4453572300ccfced8ed\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca11ba64caf1f4453572300ccfced8ed\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c5cf3686650254f44f5144222a5dff2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c5cf3686650254f44f5144222a5dff2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\11ee075c9248c74ab1545c6a17bf994e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\11ee075c9248c74ab1545c6a17bf994e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e674c2c08b74154f7b30298e327906\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e674c2c08b74154f7b30298e327906\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b512378e1c9c32ba5078cbb1f9a8e44c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b512378e1c9c32ba5078cbb1f9a8e44c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d7d90daf170696a22219bfcc22e0cd9\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d7d90daf170696a22219bfcc22e0cd9\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68f8db486281b0ce9473a27dc665756f\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68f8db486281b0ce9473a27dc665756f\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\440ca6e3fd3df4d712d302f721315852\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\440ca6e3fd3df4d712d302f721315852\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\462d21a29d239b5d9a5b25fd8c51059a\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\462d21a29d239b5d9a5b25fd8c51059a\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:15:9-63
	android:targetSdkVersion
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
uses-feature#android.hardware.camera
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:8:5-10:36
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:8:5-10:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
uses-feature#android.software.leanback
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
uses-feature#android.hardware.touchscreen
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
	tools:ignore
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:33:9-50
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68f8db486281b0ce9473a27dc665756f\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68f8db486281b0ce9473a27dc665756f\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
uses-permission#android.permission.INTERNET
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
uses-permission#android.permission.CAMERA
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:12:5-65
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:12:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
	android:minSdkVersion
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:22-65
application
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:5-343:19
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:5-343:19
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:14:5-20:19
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:14:5-20:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\881daab11df0f8fb6be0bbb2034fa98e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\881daab11df0f8fb6be0bbb2034fa98e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e10290a8de075a2af92b4127d3fcd4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e10290a8de075a2af92b4127d3fcd4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2e043999371eba3d6a775c0ebb62492e\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2e043999371eba3d6a775c0ebb62492e\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d3c30427f5b0422d5189d139ca1b0f4\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d3c30427f5b0422d5189d139ca1b0f4\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5952be130a0125f3eebf22a58b8a1ede\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5952be130a0125f3eebf22a58b8a1ede\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\58551c30a02dd338e8c26b3ad8d9086f\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\58551c30a02dd338e8c26b3ad8d9086f\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5aa0256fab7585d02a507f59930e9f5d\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5aa0256fab7585d02a507f59930e9f5d\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7969a17312b879cb64d733ab79c3405\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7969a17312b879cb64d733ab79c3405\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6bafbcd39a4eea3fe927ab325bdfa32\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6bafbcd39a4eea3fe927ab325bdfa32\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\730b3a7b75a4d2b501c35ae3324dde6e\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\730b3a7b75a4d2b501c35ae3324dde6e\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06e842e9e2185b842934479bba4f5a07\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06e842e9e2185b842934479bba4f5a07\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7a606b5c54e3225ba8edb6d80b04c104\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7a606b5c54e3225ba8edb6d80b04c104\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d6db5ad63ee943d2489e6cf90674ff7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d6db5ad63ee943d2489e6cf90674ff7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d8d479e8ce08d49f7462057f5cd16bf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d8d479e8ce08d49f7462057f5cd16bf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b67b39daef2398884ed6af353040af9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b67b39daef2398884ed6af353040af9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-35
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-41
	tools:targetApi
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:59:9-28
	android:icon
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-43
	android:allowBackup
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-35
	android:banner
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-48
	android:networkSecurityConfig
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-69
	android:usesCleartextTraffic
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:58:9-44
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-39
activity#com.mohamedrady.v2hoor.ui.MainActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:9-79:20
	android:launchMode
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-44
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-36
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:13-71:29
action#android.intent.action.MAIN
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:17-69
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-77
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:17-86
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:27-83
intent-filter#action:name:android.service.quicksettings.action.QS_TILE_PREFERENCES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:13-74:29
action#android.service.quicksettings.action.QS_TILE_PREFERENCES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:17-99
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:25-96
meta-data#android.app.shortcuts
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:13-78:53
	android:resource
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:78:17-50
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-53
activity#com.mohamedrady.v2hoor.ui.ServerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:9-83:60
	android:windowSoftInputMode
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:13-57
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-46
activity#com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:9-87:60
	android:windowSoftInputMode
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:13-57
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-58
activity#com.mohamedrady.v2hoor.ui.SettingsActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:9-90:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-48
activity#com.mohamedrady.v2hoor.ui.PerAppProxyActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:9-93:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-51
activity#com.mohamedrady.v2hoor.ui.ScannerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:9-96:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-47
activity#com.mohamedrady.v2hoor.ui.LogcatActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:9-99:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-46
activity#com.mohamedrady.v2hoor.ui.RoutingSettingActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:9-102:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-54
activity#com.mohamedrady.v2hoor.ui.RoutingEditActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:9-105:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-51
activity#com.mohamedrady.v2hoor.ui.SubSettingActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:9-108:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-50
activity#com.mohamedrady.v2hoor.ui.UserAssetActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:9-111:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-49
activity#com.mohamedrady.v2hoor.ui.UserAssetUrlActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:9-114:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:114:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-52
activity#com.mohamedrady.v2hoor.ui.SubEditActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:9-118:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-47
activity#com.mohamedrady.v2hoor.ui.ScScannerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:9-121:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-49
activity#com.mohamedrady.v2hoor.ui.ScSwitchActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:9-127:71
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-51
	android:excludeFromRecents
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-46
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:127:13-68
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-48
activity#com.mohamedrady.v2hoor.ui.UrlSchemeActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:9-147:20
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-49
intent-filter#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:text/plain
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:13-136:29
action#android.intent.action.SEND
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-69
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:25-66
category#android.intent.category.DEFAULT
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
data
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
	android:host
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
	android:scheme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
	android:mimeType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:23-52
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:install-config+data:host:install-sub+data:scheme:v2rayng
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:13-146:29
action#android.intent.action.VIEW
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
category#android.intent.category.BROWSABLE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
activity#com.mohamedrady.v2hoor.ui.CheckUpdateActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:9-150:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-51
activity#com.mohamedrady.v2hoor.ui.AboutActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:9-153:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:153:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-45
activity#com.mohamedrady.v2hoor.ui.LoginActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:9-157:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:157:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-45
activity#com.mohamedrady.v2hoor.ui.AdminPanelActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:9-161:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-50
activity#com.mohamedrady.v2hoor.ui.LogViewerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:9-165:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:164:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:13-49
activity#com.mohamedrady.v2hoor.ui.RealTimeLogActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:9-169:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:168:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:13-51
activity#com.mohamedrady.v2hoor.ui.AdminUsersActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:9-173:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:171:13-50
activity#com.mohamedrady.v2hoor.ui.AddUserActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:9-177:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:176:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:175:13-47
activity#com.mohamedrady.v2hoor.ui.UserDetailsActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:9-181:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:180:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:181:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:179:13-51
activity#com.mohamedrady.v2hoor.ui.UserServersActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:182:9-185:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:184:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:185:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:183:13-51
activity#com.mohamedrady.v2hoor.ui.AdminServersActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:186:9-189:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:188:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:187:13-52
activity#com.mohamedrady.v2hoor.ui.ServerDetailsActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:9-193:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:192:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-53
activity#com.mohamedrady.v2hoor.ui.EditServerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:9-197:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:13-50
activity#com.mohamedrady.v2hoor.ui.ServerUsersActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:9-201:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:200:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:13-51
activity#com.mohamedrady.v2hoor.ui.ServerManagementActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:9-205:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:204:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:205:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:13-56
activity#com.mohamedrady.v2hoor.ui.AddServerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:9-209:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:208:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:209:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:13-49
activity#com.mohamedrady.v2hoor.ui.ServerLogsActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:210:9-213:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:212:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:213:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:13-50
activity#com.mohamedrady.v2hoor.ui.UserManagementActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:214:9-217:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:13-54
activity#com.mohamedrady.v2hoor.ui.PromoteUserActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:9-221:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:219:13-51
activity#com.mohamedrady.v2hoor.ui.SystemSettingsActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:222:9-225:67
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:224:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:223:13-54
service#com.mohamedrady.v2hoor.service.V2RayVpnService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:227:9-244:19
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:234:13-51
	android:enabled
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:229:13-35
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:13-45
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:230:13-37
	android:permission
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:13-69
	android:foregroundServiceType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:231:13-55
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:228:13-52
intent-filter#action:name:android.net.VpnService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:235:13-237:29
action#android.net.VpnService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:17-65
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:25-62
meta-data#android.net.VpnService.SUPPORTS_ALWAYS_ON
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:238:13-240:40
	android:value
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:240:17-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:239:17-73
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:13-243:39
	android:value
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:17-76
service#com.mohamedrady.v2hoor.service.V2RayProxyOnlyService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:246:9-255:19
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:251:13-51
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:250:13-45
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:248:13-37
	android:foregroundServiceType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:249:13-55
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:13-58
service#com.mohamedrady.v2hoor.service.V2RayTestService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:257:9-260:54
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:260:13-51
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:259:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:258:13-53
receiver#com.mohamedrady.v2hoor.receiver.WidgetProvider
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:262:9-274:20
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-51
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:264:13-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:263:13-52
meta-data#android.appwidget.provider
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:13-268:63
	android:resource
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:17-60
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:17-58
intent-filter#action:name:android.appwidget.action.APPWIDGET_UPDATE+action:name:com.mohamedrady.v2hoor.action.activity+action:name:com.mohamedrady.v2hoor.action.widget.click
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:269:13-273:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:17-84
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:25-81
action#com.mohamedrady.v2hoor.action.widget.click
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:17-85
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:25-82
action#com.mohamedrady.v2hoor.action.activity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:272:17-81
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:272:25-78
receiver#com.mohamedrady.v2hoor.receiver.BootReceiver
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:275:9-282:20
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:278:13-41
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:277:13-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:276:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:279:13-281:29
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:17-79
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:25-76
service#com.mohamedrady.v2hoor.service.QSTileService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:284:9-299:19
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:291:13-51
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:289:13-50
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:286:13-36
	android:permission
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:290:13-77
	tools:targetApi
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:292:13-33
	android:icon
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:288:13-50
	android:foregroundServiceType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:287:13-55
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:285:13-50
intent-filter#action:name:android.service.quicksettings.action.QS_TILE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:293:13-295:29
action#android.service.quicksettings.action.QS_TILE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:294:17-87
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:294:25-84
activity#com.mohamedrady.v2hoor.ui.TaskerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:301:9-308:20
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:303:13-36
	android:icon
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:304:13-47
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:302:13-46
intent-filter#action:name:com.twofortyfouram.locale.intent.action.EDIT_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:305:13-307:29
action#com.twofortyfouram.locale.intent.action.EDIT_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:306:17-95
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:306:25-92
receiver#com.mohamedrady.v2hoor.receiver.TaskerReceiver
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:310:9-318:20
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:313:13-51
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:312:13-36
	tools:ignore
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:314:13-44
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:311:13-52
intent-filter#action:name:com.twofortyfouram.locale.intent.action.FIRE_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:315:13-317:29
action#com.twofortyfouram.locale.intent.action.FIRE_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:316:17-95
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:316:25-92
meta-data#androidx.work.WorkManagerInitializer
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:326:13-329:39
REJECTED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:329:17-36
	android:value
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:328:17-49
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:327:17-68
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:338:13-340:55
	android:resource
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:340:17-52
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:339:17-67
activity#io.github.g00fy2.quickie.QRScannerActivity
ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
	android:screenOrientation
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
	tools:ignore
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:19:13-42
	android:theme
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
	android:name
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
	android:enabled
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
	android:exported
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:33:13-75
	android:name
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:12:17-129
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4d074af883b3a18ffd295c39c6638600\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\58551c30a02dd338e8c26b3ad8d9086f\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\58551c30a02dd338e8c26b3ad8d9086f\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fad43229caef001b09ef193916d36a9f\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
meta-data#com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar
ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
service#androidx.work.multiprocess.RemoteWorkManagerService
ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
	android:exported
		ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
	tools:ignore
		ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:27:13-60
	android:name
		ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
