package com.mohamedrady.v2hoor.service

import android.content.Context
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Database Migration Service
 * Handles database schema migrations and data transformations
 */
class DatabaseMigration private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: DatabaseMigration? = null
        
        fun getInstance(context: Context): DatabaseMigration {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DatabaseMigration(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // Migration versions
        const val VERSION_1_0_0 = 1
        const val VERSION_1_1_0 = 2
        const val VERSION_1_2_0 = 3
        
        // Current version
        const val CURRENT_VERSION = VERSION_1_0_0
    }
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    
    /**
     * Migration interface for version-specific migrations
     */
    interface Migration {
        val fromVersion: Int
        val toVersion: Int
        suspend fun migrate(database: DatabaseReference): Result<Unit>
        fun getDescription(): String
    }
    
    /**
     * Execute all pending migrations
     */
    suspend fun executeMigrations(currentVersion: Int, targetVersion: Int): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "🔄 Starting database migrations from v$currentVersion to v$targetVersion")
                
                val migrations = getMigrations()
                val pendingMigrations = migrations.filter { 
                    it.fromVersion >= currentVersion && it.toVersion <= targetVersion 
                }.sortedBy { it.fromVersion }
                
                if (pendingMigrations.isEmpty()) {
                    android.util.Log.i(AppConfig.TAG, "✅ No migrations needed")
                    return@withContext Result.success(Unit)
                }
                
                // Execute migrations sequentially
                for (migration in pendingMigrations) {
                    android.util.Log.i(AppConfig.TAG, "🔄 Executing migration: ${migration.getDescription()}")
                    
                    val result = migration.migrate(database)
                    if (result.isFailure) {
                        android.util.Log.e(AppConfig.TAG, "❌ Migration failed: ${migration.getDescription()}")
                        return@withContext result
                    }
                    
                    // Update migration log
                    logMigration(migration)
                    
                    android.util.Log.i(AppConfig.TAG, "✅ Migration completed: ${migration.getDescription()}")
                }
                
                // Update database version
                updateDatabaseVersion(targetVersion)
                
                android.util.Log.i(AppConfig.TAG, "✅ All migrations completed successfully")
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Migration execution failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Get all available migrations
     */
    private fun getMigrations(): List<Migration> {
        return listOf(
            // Add future migrations here
            // Example: Migration_1_0_to_1_1(),
            // Migration_1_1_to_1_2()
        )
    }
    
    /**
     * Log migration execution
     */
    private suspend fun logMigration(migration: Migration) {
        try {
            val migrationLog = mapOf(
                "from_version" to migration.fromVersion,
                "to_version" to migration.toVersion,
                "description" to migration.getDescription(),
                "executed_at" to ServerValue.TIMESTAMP,
                "status" to "completed"
            )
            
            val logId = database.child("system").child("migration_logs").push().key
            if (logId != null) {
                database.child("system").child("migration_logs").child(logId).setValue(migrationLog)
            }
        } catch (e: Exception) {
            android.util.Log.w(AppConfig.TAG, "Failed to log migration", e)
        }
    }
    
    /**
     * Update database version
     */
    private suspend fun updateDatabaseVersion(version: Int) {
        suspendCancellableCoroutine<Unit> { continuation ->
            val updates = mapOf(
                "system/db_version" to version,
                "system/last_migration" to ServerValue.TIMESTAMP
            )
            
            database.updateChildren(updates)
                .addOnSuccessListener {
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Create database backup before migration
     */
    suspend fun createBackupBeforeMigration(version: Int): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "💾 Creating backup before migration to v$version")
                
                val backupId = "migration_backup_v${version}_${System.currentTimeMillis()}"
                val backupData = mapOf(
                    "id" to backupId,
                    "type" to "migration_backup",
                    "version" to version,
                    "created_at" to ServerValue.TIMESTAMP,
                    "status" to "created"
                )
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    database.child("backups").child(backupId).setValue(backupData)
                        .addOnSuccessListener {
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
                
                android.util.Log.i(AppConfig.TAG, "✅ Backup created: $backupId")
                Result.success(backupId)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Backup creation failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Validate database integrity after migration
     */
    suspend fun validateDatabaseIntegrity(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "🔍 Validating database integrity...")
                
                // Check required collections exist
                val requiredCollections = listOf("users", "servers", "subscriptions", "system")
                for (collection in requiredCollections) {
                    val exists = checkCollectionExists(collection)
                    if (!exists) {
                        throw IllegalStateException("Required collection '$collection' is missing")
                    }
                }
                
                // Check system configuration
                val systemConfigValid = validateSystemConfig()
                if (!systemConfigValid) {
                    throw IllegalStateException("System configuration is invalid")
                }
                
                // Check subscription plans
                val subscriptionsValid = validateSubscriptions()
                if (!subscriptionsValid) {
                    throw IllegalStateException("Subscription plans are invalid")
                }
                
                android.util.Log.i(AppConfig.TAG, "✅ Database integrity validation passed")
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Database integrity validation failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Check if collection exists
     */
    private suspend fun checkCollectionExists(collection: String): Boolean {
        return try {
            suspendCancellableCoroutine<Boolean> { continuation ->
                database.child(collection).addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        continuation.resume(snapshot.exists())
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        continuation.resume(false)
                    }
                })
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Validate system configuration
     */
    private suspend fun validateSystemConfig(): Boolean {
        return try {
            suspendCancellableCoroutine<Boolean> { continuation ->
                database.child("system").child("config").addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        val config = snapshot.value as? Map<*, *>
                        val isValid = config != null && 
                                     config.containsKey("app_version") &&
                                     config.containsKey("supported_protocols") &&
                                     config.containsKey("supported_languages")
                        continuation.resume(isValid)
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        continuation.resume(false)
                    }
                })
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Validate subscription plans
     */
    private suspend fun validateSubscriptions(): Boolean {
        return try {
            suspendCancellableCoroutine<Boolean> { continuation ->
                database.child("subscriptions").addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        val subscriptions = snapshot.value as? Map<*, *>
                        val isValid = subscriptions != null && 
                                     subscriptions.containsKey("free") &&
                                     subscriptions.containsKey("premium_monthly")
                        continuation.resume(isValid)
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        continuation.resume(false)
                    }
                })
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get migration history
     */
    suspend fun getMigrationHistory(): Result<List<Map<String, Any>>> {
        return withContext(Dispatchers.IO) {
            try {
                suspendCancellableCoroutine<List<Map<String, Any>>> { continuation ->
                    database.child("system").child("migration_logs")
                        .orderByChild("executed_at")
                        .addListenerForSingleValueEvent(object : ValueEventListener {
                            override fun onDataChange(snapshot: DataSnapshot) {
                                val history = mutableListOf<Map<String, Any>>()
                                for (child in snapshot.children) {
                                    val migration = child.value as? Map<String, Any>
                                    if (migration != null) {
                                        history.add(migration)
                                    }
                                }
                                continuation.resume(history)
                            }
                            
                            override fun onCancelled(error: DatabaseError) {
                                continuation.resumeWithException(error.toException())
                            }
                        })
                }
                
                Result.success(emptyList())
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to get migration history", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Example migration class (for future use)
     */
    /*
    class Migration_1_0_to_1_1 : Migration {
        override val fromVersion = VERSION_1_0_0
        override val toVersion = VERSION_1_1_0
        
        override suspend fun migrate(database: DatabaseReference): Result<Unit> {
            return try {
                // Add new fields to user profiles
                val updates = mapOf(
                    "system/features/new_feature" to true,
                    "system/config/new_setting" to "default_value"
                )
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    database.updateChildren(updates)
                        .addOnSuccessListener { continuation.resume(Unit) }
                        .addOnFailureListener { continuation.resumeWithException(it) }
                }
                
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
        
        override fun getDescription(): String {
            return "Add new user profile fields and system features"
        }
    }
    */
    
    /**
     * Emergency rollback (use with caution)
     */
    suspend fun emergencyRollback(backupId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.w(AppConfig.TAG, "⚠️ Performing emergency rollback to backup: $backupId")
                
                // This is a placeholder for rollback logic
                // In a real implementation, you would restore from the backup
                
                val rollbackLog = mapOf(
                    "backup_id" to backupId,
                    "rollback_at" to ServerValue.TIMESTAMP,
                    "reason" to "emergency_rollback",
                    "status" to "completed"
                )
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    database.child("system").child("rollback_logs").push().setValue(rollbackLog)
                        .addOnSuccessListener {
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
                
                android.util.Log.i(AppConfig.TAG, "✅ Emergency rollback completed")
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Emergency rollback failed", e)
                Result.failure(e)
            }
        }
    }
}
