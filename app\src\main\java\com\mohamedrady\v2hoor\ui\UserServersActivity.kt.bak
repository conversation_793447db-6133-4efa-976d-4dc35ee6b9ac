package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityUserServersBinding
import com.mohamedrady.v2hoor.dto.AdminServerConfig
import com.mohamedrady.v2hoor.service.AdminPermissionService
import com.mohamedrady.v2hoor.service.AdminManagementService
import com.mohamedrady.v2hoor.ui.adapter.UserServersAdapter
import com.mohamedrady.v2hoor.viewmodel.UserServersViewModel
import kotlinx.coroutines.launch

/**
 * Activity for managing user's servers (Admin only)
 */
class UserServersActivity : AppCompatActivity() {

    private lateinit var binding: ActivityUserServersBinding
    private val viewModel: UserServersViewModel by viewModels()
    private lateinit var serversAdapter: UserServersAdapter
    private val adminService = AdminPermissionService.getInstance()
    private lateinit var adminManagementService: AdminManagementService
    
    private lateinit var userId: String
    private lateinit var userName: String

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Check admin permissions
        lifecycleScope.launch {
            if (!adminService.isAdmin()) {
                Toast.makeText(this@UserServersActivity, "غير مصرح لك بالوصول لهذه الصفحة", Toast.LENGTH_SHORT).show()
                finish()
                return@launch
            }
        }

        binding = ActivityUserServersBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize admin management service
        adminManagementService = AdminManagementService.getInstance(this)

        // Get user info from intent
        userId = intent.getStringExtra("user_id") ?: run {
            Toast.makeText(this, "خطأ في معرف المستخدم", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        userName = intent.getStringExtra("user_name") ?: "مستخدم غير معروف"

        setupToolbar()
        setupRecyclerView()
        setupObservers()
        setupClickListeners()
        
        // Load data
        loadAdminUserServers()
        viewModel.loadUserServers(userId)
        viewModel.loadAvailableServers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "سيرفرات $userName"
        }
    }

    private fun setupRecyclerView() {
        // User servers adapter
        serversAdapter = UserServersAdapter(
            onRemoveServer = { server -> confirmRemoveServer(server) }
        )

        binding.recyclerViewUserServers.apply {
            layoutManager = LinearLayoutManager(this@UserServersActivity)
            adapter = serversAdapter
        }
    }

    private fun setupObservers() {
        // Observe admin management service
        adminManagementService.userServers.observe(this) { servers ->
            serversAdapter.submitList(servers.map { it.toFirebaseServerModel() })
            binding.textViewUserServersCount.text = "السيرفرات المخصصة: ${servers.size}"
        }

        // Keep existing ViewModel observers for compatibility
        viewModel.userServers.observe(this) { servers ->
            serversAdapter.submitList(servers)
            binding.textViewUserServersCount.text = "السيرفرات المخصصة: ${servers.size}"
        }

        viewModel.availableServers.observe(this) { servers ->
            // Update available servers for assignment
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading)
                android.view.View.VISIBLE else android.view.View.GONE
        }

        viewModel.error.observe(this) { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun setupClickListeners() {
        binding.fabAddServer.setOnClickListener {
            showAddServerDialog()
        }

        binding.buttonRefresh.setOnClickListener {
            viewModel.loadUserServers(userId)
            viewModel.loadAvailableServers()
        }
    }

    private fun showAddServerDialog() {
        val availableServers = viewModel.availableServers.value ?: emptyList()
        val userServers = viewModel.userServers.value ?: emptyList()
        
        // Filter out servers already assigned to user
        val unassignedServers = availableServers.filter { available ->
            userServers.none { user -> user.id == available.id }
        }

        if (unassignedServers.isEmpty()) {
            Toast.makeText(this, "لا توجد سيرفرات متاحة للإضافة", Toast.LENGTH_SHORT).show()
            return
        }

        val serverNames = unassignedServers.map { "${it.name} (${it.getLocationText()})" }.toTypedArray()
        var selectedServerIndex = -1

        AlertDialog.Builder(this)
            .setTitle("اختر سيرفر لإضافته")
            .setSingleChoiceItems(serverNames, -1) { _, which ->
                selectedServerIndex = which
            }
            .setPositiveButton("إضافة") { _, _ ->
                if (selectedServerIndex >= 0) {
                    val selectedServer = unassignedServers[selectedServerIndex]
                    assignServerToUser(selectedServer)
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun assignServerToUser(server: AdminServerConfig) {
        lifecycleScope.launch {
            viewModel.assignServerToUser(userId, server.id)
        }
    }

    private fun confirmRemoveServer(server: AdminServerConfig) {
        AlertDialog.Builder(this)
            .setTitle("تأكيد الإزالة")
            .setMessage("هل تريد إزالة السيرفر \"${server.name}\" من المستخدم $userName؟")
            .setPositiveButton("إزالة") { _, _ ->
                lifecycleScope.launch {
                    viewModel.removeServerFromUser(userId, server.id)
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_user_servers, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_assign_all -> {
                showAssignAllDialog()
                true
            }
            R.id.action_remove_all -> {
                showRemoveAllDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showAssignAllDialog() {
        AlertDialog.Builder(this)
            .setTitle("تخصيص جميع السيرفرات")
            .setMessage("هل تريد تخصيص جميع السيرفرات المتاحة للمستخدم $userName؟")
            .setPositiveButton("نعم") { _, _ ->
                lifecycleScope.launch {
                    viewModel.assignAllServersToUser(userId)
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showRemoveAllDialog() {
        AlertDialog.Builder(this)
            .setTitle("إزالة جميع السيرفرات")
            .setMessage("هل تريد إزالة جميع السيرفرات من المستخدم $userName؟")
            .setPositiveButton("إزالة") { _, _ ->
                lifecycleScope.launch {
                    viewModel.removeAllServersFromUser(userId)
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    /**
     * Load user servers using admin management service
     */
    private fun loadAdminUserServers() {
        lifecycleScope.launch {
            try {
                val result = adminManagementService.loadUserServers(userId)
                if (result.isFailure) {
                    val error = result.exceptionOrNull()
                    com.mohamedrady.v2hoor.util.CrashHandler.logError("UserServers", "Failed to load user servers", error)
                    Toast.makeText(this@UserServersActivity, "فشل في تحميل السيرفرات: ${error?.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("UserServers", "Error loading user servers", e)
                Toast.makeText(this@UserServersActivity, "خطأ في تحميل السيرفرات", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh data when returning to activity
        loadAdminUserServers()
        viewModel.loadUserServers(userId)
    }
}
