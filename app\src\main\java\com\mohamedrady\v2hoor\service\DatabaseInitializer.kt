package com.mohamedrady.v2hoor.service

import android.content.Context
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Database Initializer Service
 * Handles database initialization, default data setup, and schema migration
 */
class DatabaseInitializer private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: DatabaseInitializer? = null
        
        fun getInstance(context: Context): DatabaseInitializer {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DatabaseInitializer(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val DB_VERSION_KEY = "db_version"
        private const val CURRENT_DB_VERSION = 1
        private const val INIT_STATUS_KEY = "init_status"
    }
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    
    /**
     * Initialize the database with default data and configuration
     */
    suspend fun initializeDatabase(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.i(AppConfig.TAG, "🗄️ Starting database initialization...")
                
                // Check if database is already initialized
                val isInitialized = checkInitializationStatus()
                if (isInitialized) {
                    android.util.Log.i(AppConfig.TAG, "✅ Database already initialized")
                    return@withContext Result.success(Unit)
                }
                
                // Initialize system configuration
                initializeSystemConfig()
                
                // Initialize subscription plans
                initializeSubscriptionPlans()
                
                // Initialize default servers (if any)
                initializeDefaultServers()
                
                // Initialize admin user
                initializeAdminUser()
                
                // Set initialization status
                markAsInitialized()
                
                android.util.Log.i(AppConfig.TAG, "✅ Database initialization completed successfully")
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Database initialization failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Check if database is already initialized
     */
    private suspend fun checkInitializationStatus(): Boolean {
        return try {
            suspendCancellableCoroutine<Boolean> { continuation ->
                database.child("system").child(INIT_STATUS_KEY)
                    .addListenerForSingleValueEvent(object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            val isInitialized = snapshot.getValue(Boolean::class.java) ?: false
                            continuation.resume(isInitialized)
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            continuation.resume(false)
                        }
                    })
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Initialize system configuration
     */
    private suspend fun initializeSystemConfig() {
        android.util.Log.i(AppConfig.TAG, "⚙️ Initializing system configuration...")
        
        val systemConfig = mapOf(
            "config" to mapOf(
                "app_version" to "1.0.0",
                "min_supported_version" to "0.9.0",
                "maintenance_mode" to false,
                "registration_enabled" to true,
                "free_tier_enabled" to true,
                "max_free_users" to 5000,
                "default_subscription" to "free",
                "supported_protocols" to listOf("vmess", "vless", "trojan", "shadowsocks"),
                "supported_languages" to listOf("ar", "en"),
                "default_language" to "ar",
                "created_at" to ServerValue.TIMESTAMP,
                "updated_at" to ServerValue.TIMESTAMP
            ),
            "features" to mapOf(
                "user_registration" to true,
                "social_login" to false,
                "referral_program" to true,
                "affiliate_program" to false,
                "multi_device" to true,
                "kill_switch" to true,
                "split_tunneling" to true,
                "ad_blocking" to true,
                "created_at" to ServerValue.TIMESTAMP
            ),
            "limits" to mapOf(
                "max_devices_per_user" to 5,
                "max_sessions_per_user" to 3,
                "max_bandwidth_per_user_mbps" to 100,
                "session_timeout_minutes" to 1440,
                "idle_timeout_minutes" to 30,
                "created_at" to ServerValue.TIMESTAMP
            ),
            "notifications" to mapOf(
                "maintenance_alerts" to true,
                "security_updates" to true,
                "feature_announcements" to true,
                "promotional_offers" to false,
                "created_at" to ServerValue.TIMESTAMP
            ),
            DB_VERSION_KEY to CURRENT_DB_VERSION,
            "created_at" to ServerValue.TIMESTAMP,
            "updated_at" to ServerValue.TIMESTAMP
        )
        
        suspendCancellableCoroutine<Unit> { continuation ->
            database.child("system").setValue(systemConfig)
                .addOnSuccessListener {
                    android.util.Log.i(AppConfig.TAG, "✅ System configuration initialized")
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    android.util.Log.e(AppConfig.TAG, "❌ Failed to initialize system config", exception)
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Initialize subscription plans
     */
    private suspend fun initializeSubscriptionPlans() {
        android.util.Log.i(AppConfig.TAG, "💳 Initializing subscription plans...")
        
        val subscriptionPlans = mapOf(
            "free" to mapOf(
                "id" to "free",
                "name" to "Free Plan",
                "description" to "Basic VPN access with limited features",
                "type" to "free",
                "duration_days" to -1, // Unlimited duration
                "price" to mapOf(
                    "amount" to 0.0,
                    "currency" to "USD",
                    "billing_cycle" to "none"
                ),
                "features" to mapOf(
                    "unlimited_bandwidth" to false,
                    "premium_servers" to false,
                    "max_devices" to 1,
                    "data_limit_gb" to 10,
                    "speed_limit_mbps" to 10,
                    "support_level" to "community",
                    "ad_free" to false,
                    "kill_switch" to true,
                    "split_tunneling" to false
                ),
                "server_access" to mapOf(
                    "free_servers" to true,
                    "premium_servers" to false,
                    "exclusive_servers" to false,
                    "server_locations" to listOf("US", "UK", "DE")
                ),
                "trial" to mapOf(
                    "available" to false,
                    "duration_days" to 0,
                    "requires_payment_method" to false
                ),
                "status" to "active",
                "created_at" to ServerValue.TIMESTAMP,
                "updated_at" to ServerValue.TIMESTAMP
            ),
            "premium_monthly" to mapOf(
                "id" to "premium_monthly",
                "name" to "Premium Monthly",
                "description" to "Full access to all premium features",
                "type" to "premium",
                "duration_days" to 30,
                "price" to mapOf(
                    "amount" to 9.99,
                    "currency" to "USD",
                    "billing_cycle" to "monthly"
                ),
                "features" to mapOf(
                    "unlimited_bandwidth" to true,
                    "premium_servers" to true,
                    "max_devices" to 5,
                    "data_limit_gb" to null,
                    "speed_limit_mbps" to null,
                    "support_level" to "priority",
                    "ad_free" to true,
                    "kill_switch" to true,
                    "split_tunneling" to true
                ),
                "server_access" to mapOf(
                    "free_servers" to true,
                    "premium_servers" to true,
                    "exclusive_servers" to false,
                    "server_locations" to listOf("*")
                ),
                "trial" to mapOf(
                    "available" to true,
                    "duration_days" to 7,
                    "requires_payment_method" to true
                ),
                "status" to "active",
                "created_at" to ServerValue.TIMESTAMP,
                "updated_at" to ServerValue.TIMESTAMP
            ),
            "premium_yearly" to mapOf(
                "id" to "premium_yearly",
                "name" to "Premium Yearly",
                "description" to "Full premium access with 2 months free",
                "type" to "premium",
                "duration_days" to 365,
                "price" to mapOf(
                    "amount" to 99.99,
                    "currency" to "USD",
                    "billing_cycle" to "yearly"
                ),
                "features" to mapOf(
                    "unlimited_bandwidth" to true,
                    "premium_servers" to true,
                    "max_devices" to 10,
                    "data_limit_gb" to null,
                    "speed_limit_mbps" to null,
                    "support_level" to "priority",
                    "ad_free" to true,
                    "kill_switch" to true,
                    "split_tunneling" to true
                ),
                "server_access" to mapOf(
                    "free_servers" to true,
                    "premium_servers" to true,
                    "exclusive_servers" to true,
                    "server_locations" to listOf("*")
                ),
                "trial" to mapOf(
                    "available" to true,
                    "duration_days" to 14,
                    "requires_payment_method" to true
                ),
                "status" to "active",
                "created_at" to ServerValue.TIMESTAMP,
                "updated_at" to ServerValue.TIMESTAMP
            )
        )
        
        suspendCancellableCoroutine<Unit> { continuation ->
            database.child("subscriptions").setValue(subscriptionPlans)
                .addOnSuccessListener {
                    android.util.Log.i(AppConfig.TAG, "✅ Subscription plans initialized")
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    android.util.Log.e(AppConfig.TAG, "❌ Failed to initialize subscription plans", exception)
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Initialize default servers (if any)
     */
    private suspend fun initializeDefaultServers() {
        android.util.Log.i(AppConfig.TAG, "🖥️ Initializing default servers...")
        
        // For now, we'll create a placeholder structure
        // Actual servers will be added by admins through the admin panel
        val defaultServers = mapOf(
            "placeholder" to mapOf(
                "id" to "placeholder",
                "name" to "Placeholder Server",
                "description" to "This is a placeholder server entry",
                "status" to "disabled",
                "type" to "free",
                "protocol" to "vmess",
                "config" to mapOf(
                    "server" to "placeholder.v2hoor.com",
                    "port" to 443,
                    "uuid" to "00000000-0000-0000-0000-000000000000"
                ),
                "location" to mapOf(
                    "country" to "Unknown",
                    "country_code" to "XX",
                    "city" to "Unknown",
                    "region" to "Unknown",
                    "flag" to "🏳️"
                ),
                "created_at" to ServerValue.TIMESTAMP,
                "updated_at" to ServerValue.TIMESTAMP,
                "created_by" to "system"
            )
        )
        
        suspendCancellableCoroutine<Unit> { continuation ->
            database.child("servers").setValue(defaultServers)
                .addOnSuccessListener {
                    android.util.Log.i(AppConfig.TAG, "✅ Default servers initialized")
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    android.util.Log.e(AppConfig.TAG, "❌ Failed to initialize default servers", exception)
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Initialize admin user
     */
    private suspend fun initializeAdminUser() {
        android.util.Log.i(AppConfig.TAG, "👑 Initializing admin user...")
        
        val adminUsers = mapOf(
            "super_admin" to mapOf(
                "email" to "<EMAIL>",
                "role" to "super_admin",
                "permissions" to listOf("*"),
                "created_at" to ServerValue.TIMESTAMP,
                "last_login" to 0,
                "login_count" to 0,
                "status" to "active"
            )
        )
        
        suspendCancellableCoroutine<Unit> { continuation ->
            database.child("admin").child("users").setValue(adminUsers)
                .addOnSuccessListener {
                    android.util.Log.i(AppConfig.TAG, "✅ Admin user initialized")
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    android.util.Log.e(AppConfig.TAG, "❌ Failed to initialize admin user", exception)
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Mark database as initialized
     */
    private suspend fun markAsInitialized() {
        suspendCancellableCoroutine<Unit> { continuation ->
            val updates = mapOf(
                "system/$INIT_STATUS_KEY" to true,
                "system/initialized_at" to ServerValue.TIMESTAMP,
                "system/initialized_by" to "system"
            )
            
            database.updateChildren(updates)
                .addOnSuccessListener {
                    android.util.Log.i(AppConfig.TAG, "✅ Database marked as initialized")
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    android.util.Log.e(AppConfig.TAG, "❌ Failed to mark database as initialized", exception)
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Check and perform database migration if needed
     */
    suspend fun checkAndMigrate(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val currentVersion = getCurrentDatabaseVersion()
                
                if (currentVersion < CURRENT_DB_VERSION) {
                    android.util.Log.i(AppConfig.TAG, "🔄 Database migration needed: $currentVersion -> $CURRENT_DB_VERSION")
                    performMigration(currentVersion, CURRENT_DB_VERSION)
                } else {
                    android.util.Log.i(AppConfig.TAG, "✅ Database is up to date (version $currentVersion)")
                }
                
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Database migration failed", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Get current database version
     */
    private suspend fun getCurrentDatabaseVersion(): Int {
        return try {
            suspendCancellableCoroutine<Int> { continuation ->
                database.child("system").child(DB_VERSION_KEY)
                    .addListenerForSingleValueEvent(object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            val version = snapshot.getValue(Int::class.java) ?: 0
                            continuation.resume(version)
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            continuation.resume(0)
                        }
                    })
            }
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * Perform database migration
     */
    private suspend fun performMigration(fromVersion: Int, toVersion: Int) {
        android.util.Log.i(AppConfig.TAG, "🔄 Performing database migration from $fromVersion to $toVersion")
        
        // Add migration logic here as needed
        // For now, just update the version number
        
        suspendCancellableCoroutine<Unit> { continuation ->
            val updates = mapOf(
                "system/$DB_VERSION_KEY" to toVersion,
                "system/last_migration" to ServerValue.TIMESTAMP,
                "system/migration_from" to fromVersion,
                "system/migration_to" to toVersion
            )
            
            database.updateChildren(updates)
                .addOnSuccessListener {
                    android.util.Log.i(AppConfig.TAG, "✅ Database migration completed")
                    continuation.resume(Unit)
                }
                .addOnFailureListener { exception ->
                    android.util.Log.e(AppConfig.TAG, "❌ Database migration failed", exception)
                    continuation.resumeWithException(exception)
                }
        }
    }
    
    /**
     * Reset database (for development/testing only)
     */
    suspend fun resetDatabase(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                android.util.Log.w(AppConfig.TAG, "⚠️ Resetting database...")
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    database.child("system").child(INIT_STATUS_KEY).setValue(false)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "✅ Database reset completed")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "❌ Database reset failed", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "❌ Database reset failed", e)
                Result.failure(e)
            }
        }
    }
}
