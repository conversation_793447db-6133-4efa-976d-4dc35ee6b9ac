<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="subscription_status_banner" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\subscription_status_banner.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/subscription_banner"><Targets><Target id="@+id/subscription_banner" tag="layout/subscription_status_banner_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="104" endOffset="14"/></Target><Target id="@+id/iv_subscription_icon" view="ImageView"><Expressions/><location startLine="13" startOffset="4" endLine="19" endOffset="71"/></Target><Target id="@+id/tv_subscription_status" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="36" endOffset="64"/></Target><Target id="@+id/tv_subscription_details" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="46" endOffset="44"/></Target><Target id="@+id/layout_data_usage" view="LinearLayout"><Expressions/><location startLine="49" startOffset="8" endLine="76" endOffset="22"/></Target><Target id="@+id/progress_data_usage" view="ProgressBar"><Expressions/><location startLine="57" startOffset="12" endLine="65" endOffset="38"/></Target><Target id="@+id/tv_data_usage" view="TextView"><Expressions/><location startLine="67" startOffset="12" endLine="74" endOffset="50"/></Target><Target id="@+id/btn_subscription_action" view="Button"><Expressions/><location startLine="81" startOffset="4" endLine="91" endOffset="35"/></Target><Target id="@+id/btn_close_banner" view="ImageButton"><Expressions/><location startLine="94" startOffset="4" endLine="102" endOffset="35"/></Target></Targets></Layout>