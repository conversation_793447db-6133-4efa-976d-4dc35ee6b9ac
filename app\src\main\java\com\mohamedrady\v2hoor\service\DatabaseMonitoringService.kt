package com.mohamedrady.v2hoor.service

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import kotlinx.coroutines.*
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Database Monitoring and Analytics Service
 * Monitors database performance, usage, and health metrics
 */
class DatabaseMonitoringService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: DatabaseMonitoringService? = null
        
        fun getInstance(context: Context): DatabaseMonitoringService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DatabaseMonitoringService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val MONITORING_INTERVAL = 60000L // 1 minute
        private const val ANALYTICS_UPDATE_INTERVAL = 300000L // 5 minutes
        private const val HEALTH_CHECK_INTERVAL = 30000L // 30 seconds
    }
    
    data class DatabaseMetrics(
        val totalUsers: Long = 0,
        val activeUsers: Long = 0,
        val totalServers: Long = 0,
        val activeServers: Long = 0,
        val totalSessions: Long = 0,
        val activeSessions: Long = 0,
        val totalDataTransfer: Long = 0,
        val averageResponseTime: Double = 0.0,
        val errorRate: Double = 0.0,
        val lastUpdated: Long = System.currentTimeMillis()
    )
    
    data class PerformanceMetrics(
        val readLatency: Double = 0.0,
        val writeLatency: Double = 0.0,
        val connectionCount: Int = 0,
        val queryCount: Long = 0,
        val errorCount: Long = 0,
        val cacheHitRate: Double = 0.0,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    data class HealthStatus(
        val isHealthy: Boolean = true,
        val issues: List<String> = emptyList(),
        val lastCheck: Long = System.currentTimeMillis(),
        val uptime: Long = 0,
        val responseTime: Double = 0.0
    )
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // LiveData for real-time metrics
    private val _databaseMetrics = MutableLiveData<DatabaseMetrics>()
    val databaseMetrics: LiveData<DatabaseMetrics> = _databaseMetrics
    
    private val _performanceMetrics = MutableLiveData<PerformanceMetrics>()
    val performanceMetrics: LiveData<PerformanceMetrics> = _performanceMetrics
    
    private val _healthStatus = MutableLiveData<HealthStatus>()
    val healthStatus: LiveData<HealthStatus> = _healthStatus
    
    // Monitoring jobs
    private var monitoringJob: Job? = null
    private var analyticsJob: Job? = null
    private var healthCheckJob: Job? = null
    
    // Performance tracking
    private val queryTimes = mutableListOf<Long>()
    private var totalQueries = 0L
    private var totalErrors = 0L
    
    /**
     * Start monitoring services
     */
    fun startMonitoring() {
        android.util.Log.i(AppConfig.TAG, "📊 Starting database monitoring...")
        
        stopMonitoring() // Stop any existing monitoring
        
        // Start metrics collection
        monitoringJob = scope.launch {
            while (isActive) {
                try {
                    collectDatabaseMetrics()
                    delay(MONITORING_INTERVAL)
                } catch (e: Exception) {
                    android.util.Log.e(AppConfig.TAG, "Error in metrics collection", e)
                    delay(MONITORING_INTERVAL)
                }
            }
        }
        
        // Start analytics updates
        analyticsJob = scope.launch {
            while (isActive) {
                try {
                    updateAnalytics()
                    delay(ANALYTICS_UPDATE_INTERVAL)
                } catch (e: Exception) {
                    android.util.Log.e(AppConfig.TAG, "Error in analytics update", e)
                    delay(ANALYTICS_UPDATE_INTERVAL)
                }
            }
        }
        
        // Start health checks
        healthCheckJob = scope.launch {
            while (isActive) {
                try {
                    performHealthCheck()
                    delay(HEALTH_CHECK_INTERVAL)
                } catch (e: Exception) {
                    android.util.Log.e(AppConfig.TAG, "Error in health check", e)
                    delay(HEALTH_CHECK_INTERVAL)
                }
            }
        }
        
        android.util.Log.i(AppConfig.TAG, "✅ Database monitoring started")
    }
    
    /**
     * Stop monitoring services
     */
    fun stopMonitoring() {
        monitoringJob?.cancel()
        analyticsJob?.cancel()
        healthCheckJob?.cancel()
        
        android.util.Log.i(AppConfig.TAG, "🛑 Database monitoring stopped")
    }
    
    /**
     * Collect database metrics
     */
    private suspend fun collectDatabaseMetrics() {
        try {
            val startTime = System.currentTimeMillis()
            
            // Collect user metrics
            val totalUsers = countCollection("users")
            val activeUsers = countActiveUsers()
            
            // Collect server metrics
            val totalServers = countCollection("servers")
            val activeServers = countActiveServers()
            
            // Collect session metrics
            val totalSessions = countTotalSessions()
            val activeSessions = countActiveSessions()
            
            // Collect data transfer metrics
            val totalDataTransfer = calculateTotalDataTransfer()
            
            // Calculate response time
            val responseTime = System.currentTimeMillis() - startTime
            updatePerformanceMetrics(responseTime.toDouble())
            
            // Calculate error rate
            val errorRate = if (totalQueries > 0) (totalErrors.toDouble() / totalQueries) * 100 else 0.0
            
            val metrics = DatabaseMetrics(
                totalUsers = totalUsers,
                activeUsers = activeUsers,
                totalServers = totalServers,
                activeServers = activeServers,
                totalSessions = totalSessions,
                activeSessions = activeSessions,
                totalDataTransfer = totalDataTransfer,
                averageResponseTime = calculateAverageResponseTime(),
                errorRate = errorRate,
                lastUpdated = System.currentTimeMillis()
            )
            
            _databaseMetrics.postValue(metrics)
            
            android.util.Log.d(AppConfig.TAG, "📊 Metrics collected: Users=$totalUsers, Servers=$totalServers, Sessions=$activeSessions")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to collect database metrics", e)
            totalErrors++
        }
    }
    
    /**
     * Count documents in a collection
     */
    private suspend fun countCollection(collection: String): Long {
        return try {
            suspendCancellableCoroutine { continuation ->
                val startTime = System.currentTimeMillis()
                
                database.child(collection).addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        val count = snapshot.childrenCount
                        val queryTime = System.currentTimeMillis() - startTime
                        recordQueryTime(queryTime)
                        continuation.resume(count)
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        totalErrors++
                        continuation.resumeWithException(error.toException())
                    }
                })
            }
        } catch (e: Exception) {
            totalErrors++
            0L
        }
    }
    
    /**
     * Count active users (logged in within last 24 hours)
     */
    private suspend fun countActiveUsers(): Long {
        return try {
            val oneDayAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000)
            
            suspendCancellableCoroutine { continuation ->
                database.child("users")
                    .orderByChild("profile/last_login")
                    .startAt(oneDayAgo.toDouble())
                    .addListenerForSingleValueEvent(object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            continuation.resume(snapshot.childrenCount)
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            totalErrors++
                            continuation.resume(0L)
                        }
                    })
            }
        } catch (e: Exception) {
            totalErrors++
            0L
        }
    }
    
    /**
     * Count active servers
     */
    private suspend fun countActiveServers(): Long {
        return try {
            suspendCancellableCoroutine { continuation ->
                database.child("servers")
                    .orderByChild("status")
                    .equalTo("active")
                    .addListenerForSingleValueEvent(object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            continuation.resume(snapshot.childrenCount)
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            totalErrors++
                            continuation.resume(0L)
                        }
                    })
            }
        } catch (e: Exception) {
            totalErrors++
            0L
        }
    }
    
    /**
     * Count total sessions
     */
    private suspend fun countTotalSessions(): Long {
        return try {
            var totalSessions = 0L
            
            suspendCancellableCoroutine<Unit> { continuation ->
                database.child("users").addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        for (userSnapshot in snapshot.children) {
                            val sessionsSnapshot = userSnapshot.child("stats/sessions")
                            totalSessions += sessionsSnapshot.childrenCount
                        }
                        continuation.resume(Unit)
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        totalErrors++
                        continuation.resumeWithException(error.toException())
                    }
                })
            }
            
            totalSessions
        } catch (e: Exception) {
            totalErrors++
            0L
        }
    }
    
    /**
     * Count active sessions (ongoing connections)
     */
    private suspend fun countActiveSessions(): Long {
        return try {
            var activeSessions = 0L
            
            suspendCancellableCoroutine<Unit> { continuation ->
                database.child("users").addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        for (userSnapshot in snapshot.children) {
                            val sessionsSnapshot = userSnapshot.child("stats/sessions")
                            for (sessionSnapshot in sessionsSnapshot.children) {
                                val endTime = sessionSnapshot.child("end_time").getValue(Long::class.java) ?: 0
                                if (endTime == 0L) { // Session is still active
                                    activeSessions++
                                }
                            }
                        }
                        continuation.resume(Unit)
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        totalErrors++
                        continuation.resumeWithException(error.toException())
                    }
                })
            }
            
            activeSessions
        } catch (e: Exception) {
            totalErrors++
            0L
        }
    }
    
    /**
     * Calculate total data transfer
     */
    private suspend fun calculateTotalDataTransfer(): Long {
        return try {
            var totalBytes = 0L
            
            suspendCancellableCoroutine<Unit> { continuation ->
                database.child("users").addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        for (userSnapshot in snapshot.children) {
                            val sessionsSnapshot = userSnapshot.child("stats/sessions")
                            for (sessionSnapshot in sessionsSnapshot.children) {
                                val bytesSent = sessionSnapshot.child("bytes_sent").getValue(Long::class.java) ?: 0
                                val bytesReceived = sessionSnapshot.child("bytes_received").getValue(Long::class.java) ?: 0
                                totalBytes += bytesSent + bytesReceived
                            }
                        }
                        continuation.resume(Unit)
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        totalErrors++
                        continuation.resumeWithException(error.toException())
                    }
                })
            }
            
            totalBytes
        } catch (e: Exception) {
            totalErrors++
            0L
        }
    }
    
    /**
     * Record query execution time
     */
    private fun recordQueryTime(timeMs: Long) {
        synchronized(queryTimes) {
            queryTimes.add(timeMs)
            totalQueries++
            
            // Keep only recent query times (last 100)
            if (queryTimes.size > 100) {
                queryTimes.removeAt(0)
            }
        }
    }
    
    /**
     * Calculate average response time
     */
    private fun calculateAverageResponseTime(): Double {
        synchronized(queryTimes) {
            return if (queryTimes.isNotEmpty()) {
                queryTimes.average()
            } else {
                0.0
            }
        }
    }
    
    /**
     * Update performance metrics
     */
    private fun updatePerformanceMetrics(responseTime: Double) {
        val metrics = PerformanceMetrics(
            readLatency = responseTime,
            writeLatency = responseTime, // Simplified - would track separately in real implementation
            connectionCount = 1, // Simplified
            queryCount = totalQueries,
            errorCount = totalErrors,
            cacheHitRate = 0.0, // Would be calculated based on Firebase caching
            timestamp = System.currentTimeMillis()
        )
        
        _performanceMetrics.postValue(metrics)
    }
    
    /**
     * Perform health check
     */
    private suspend fun performHealthCheck() {
        try {
            val startTime = System.currentTimeMillis()
            val issues = mutableListOf<String>()
            
            // Test database connectivity
            val isConnected = testDatabaseConnection()
            if (!isConnected) {
                issues.add("Database connection failed")
            }
            
            // Check response time
            val responseTime = System.currentTimeMillis() - startTime
            if (responseTime > 5000) { // 5 seconds threshold
                issues.add("High response time: ${responseTime}ms")
            }
            
            // Check error rate
            val errorRate = if (totalQueries > 0) (totalErrors.toDouble() / totalQueries) * 100 else 0.0
            if (errorRate > 5.0) { // 5% error rate threshold
                issues.add("High error rate: ${String.format("%.2f", errorRate)}%")
            }
            
            val healthStatus = HealthStatus(
                isHealthy = issues.isEmpty(),
                issues = issues,
                lastCheck = System.currentTimeMillis(),
                uptime = System.currentTimeMillis() - startTime, // Simplified
                responseTime = responseTime.toDouble()
            )
            
            _healthStatus.postValue(healthStatus)
            
            if (issues.isNotEmpty()) {
                android.util.Log.w(AppConfig.TAG, "⚠️ Database health issues: ${issues.joinToString(", ")}")
            }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Health check failed", e)
            
            val healthStatus = HealthStatus(
                isHealthy = false,
                issues = listOf("Health check failed: ${e.message}"),
                lastCheck = System.currentTimeMillis(),
                responseTime = -1.0
            )
            
            _healthStatus.postValue(healthStatus)
        }
    }
    
    /**
     * Test database connection
     */
    private suspend fun testDatabaseConnection(): Boolean {
        return try {
            suspendCancellableCoroutine { continuation ->
                database.child("system/config/app_version").addListenerForSingleValueEvent(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        continuation.resume(true)
                    }
                    
                    override fun onCancelled(error: DatabaseError) {
                        continuation.resume(false)
                    }
                })
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Update analytics data
     */
    private suspend fun updateAnalytics() {
        try {
            val currentMetrics = _databaseMetrics.value ?: return
            
            val analyticsData = mapOf(
                "timestamp" to ServerValue.TIMESTAMP,
                "total_users" to currentMetrics.totalUsers,
                "active_users" to currentMetrics.activeUsers,
                "total_servers" to currentMetrics.totalServers,
                "active_servers" to currentMetrics.activeServers,
                "total_sessions" to currentMetrics.totalSessions,
                "active_sessions" to currentMetrics.activeSessions,
                "total_data_transfer" to currentMetrics.totalDataTransfer,
                "average_response_time" to currentMetrics.averageResponseTime,
                "error_rate" to currentMetrics.errorRate
            )
            
            // Store daily analytics
            val dateKey = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
            database.child("analytics/daily").child(dateKey).setValue(analyticsData)
            
            // Update global analytics
            database.child("analytics/global").setValue(analyticsData)
            
            android.util.Log.d(AppConfig.TAG, "📈 Analytics updated for $dateKey")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to update analytics", e)
        }
    }
    
    /**
     * Get monitoring statistics
     */
    fun getMonitoringStats(): Map<String, String> {
        val currentMetrics = _databaseMetrics.value
        val performanceMetrics = _performanceMetrics.value
        val healthStatus = _healthStatus.value
        
        return mapOf(
            "total_users" to (currentMetrics?.totalUsers?.toString() ?: "0"),
            "active_users" to (currentMetrics?.activeUsers?.toString() ?: "0"),
            "total_servers" to (currentMetrics?.totalServers?.toString() ?: "0"),
            "active_servers" to (currentMetrics?.activeServers?.toString() ?: "0"),
            "active_sessions" to (currentMetrics?.activeSessions?.toString() ?: "0"),
            "average_response_time" to String.format("%.2f ms", currentMetrics?.averageResponseTime ?: 0.0),
            "error_rate" to String.format("%.2f%%", currentMetrics?.errorRate ?: 0.0),
            "total_queries" to totalQueries.toString(),
            "total_errors" to totalErrors.toString(),
            "health_status" to if (healthStatus?.isHealthy == true) "Healthy" else "Issues Detected",
            "last_updated" to Date(currentMetrics?.lastUpdated ?: 0).toString()
        )
    }
    
    /**
     * Export monitoring data
     */
    suspend fun exportMonitoringData(): Result<String> {
        return try {
            val exportData = mapOf(
                "database_metrics" to _databaseMetrics.value,
                "performance_metrics" to _performanceMetrics.value,
                "health_status" to _healthStatus.value,
                "monitoring_stats" to getMonitoringStats(),
                "exported_at" to System.currentTimeMillis()
            )
            
            val jsonData = org.json.JSONObject(exportData).toString(2)
            Result.success(jsonData)
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to export monitoring data", e)
            Result.failure(e)
        }
    }
    
    /**
     * Cleanup monitoring service
     */
    fun cleanup() {
        stopMonitoring()
        scope.cancel()
    }
    
    private fun SimpleDateFormat(pattern: String, locale: Locale) = java.text.SimpleDateFormat(pattern, locale)
}
