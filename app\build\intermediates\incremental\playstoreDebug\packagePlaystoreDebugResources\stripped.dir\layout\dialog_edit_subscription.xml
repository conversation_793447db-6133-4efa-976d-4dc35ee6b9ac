<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Subscription End Date -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="تاريخ انتهاء الاشتراك"
        app:helperText="تنسيق التاريخ: yyyy-MM-dd"
        app:helperTextEnabled="true"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_subscription_end"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="date"
            android:hint="2025-12-31" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Subscription Type -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="نوع الاشتراك"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

        <AutoCompleteTextView
            android:id="@+id/et_subscription_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:text="basic" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Data Limit -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="حد البيانات (GB)"
        app:helperText="0 = غير محدود"
        app:helperTextEnabled="true"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_data_limit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:hint="100" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Max Devices -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="عدد الأجهزة المسموح"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_max_devices"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:hint="3" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Notes -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="ملاحظات"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:lines="3"
            android:hint="ملاحظات إضافية..." />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Quick Actions -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="إجراءات سريعة"
        android:textStyle="bold"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_extend_1_month"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="شهر واحد"
            android:textSize="12sp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_extend_3_months"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:text="3 أشهر"
            android:textSize="12sp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_extend_1_year"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="سنة واحدة"
            android:textSize="12sp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_unlimited"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="غير محدود"
            android:textSize="12sp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_deactivate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="إيقاف الاشتراك"
            android:textSize="12sp"
            android:textColor="@color/red"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

    </LinearLayout>

</LinearLayout>
