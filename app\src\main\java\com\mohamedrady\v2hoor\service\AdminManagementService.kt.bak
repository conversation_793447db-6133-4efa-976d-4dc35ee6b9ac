package com.mohamedrady.v2hoor.service

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.AdminUserModel
import com.mohamedrady.v2hoor.model.AdminServerModel
import com.mohamedrady.v2hoor.model.UserProfile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Admin Management Service
 * Handles admin operations for user and server management
 */
class AdminManagementService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: AdminManagementService? = null
        
        fun getInstance(context: Context): AdminManagementService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AdminManagementService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val USERS_PATH = "users"
        private const val SERVERS_PATH = "servers"
    }
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val userRoleService = UserRoleService.getInstance(context)
    private val roleBasedAccessControl = RoleBasedAccessControl.getInstance(context)
    
    // LiveData for admin dashboard
    private val _allUsers = MutableLiveData<List<AdminUserModel>>()
    val allUsers: LiveData<List<AdminUserModel>> = _allUsers
    
    private val _userServers = MutableLiveData<List<AdminServerModel>>()
    val userServers: LiveData<List<AdminServerModel>> = _userServers
    
    private val _adminStats = MutableLiveData<AdminStats>()
    val adminStats: LiveData<AdminStats> = _adminStats
    
    /**
     * Admin statistics data class
     */
    data class AdminStats(
        val totalUsers: Int = 0,
        val activeUsers: Int = 0,
        val expiredUsers: Int = 0,
        val adminUsers: Int = 0,
        val totalServers: Int = 0,
        val activeServers: Int = 0,
        val totalDataUsage: Long = 0,
        val averageDataUsage: Float = 0f
    )
    
    /**
     * Load all users for admin management
     */
    suspend fun loadAllUsers(): Result<List<AdminUserModel>> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_USERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }
                
                suspendCancellableCoroutine<List<AdminUserModel>> { continuation ->
                    val usersRef = database.child(USERS_PATH)
                    
                    val listener = object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            try {
                                val users = mutableListOf<AdminUserModel>()
                                
                                for (userSnapshot in snapshot.children) {
                                    val uid = userSnapshot.key ?: continue
                                    val userProfile = userSnapshot.getValue(UserProfile::class.java)
                                    
                                    if (userProfile != null) {
                                        val adminUser = AdminUserModel.fromUserProfile(userProfile.copy(uid = uid))
                                        
                                        // Count servers for this user
                                        val serversSnapshot = userSnapshot.child("servers")
                                        adminUser.serverCount = serversSnapshot.childrenCount.toInt()
                                        
                                        users.add(adminUser)
                                    }
                                }
                                
                                // Sort users by creation date (newest first)
                                users.sortByDescending { it.createdAt }
                                
                                // Update LiveData
                                _allUsers.postValue(users)
                                
                                // Update admin stats
                                updateAdminStats(users)
                                
                                android.util.Log.i(AppConfig.TAG, "Loaded ${users.size} users for admin management")
                                continuation.resume(users)
                            } catch (e: Exception) {
                                android.util.Log.e(AppConfig.TAG, "Error parsing users data", e)
                                continuation.resumeWithException(e)
                            }
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            android.util.Log.e(AppConfig.TAG, "Firebase query cancelled: ${error.message}")
                            continuation.resumeWithException(error.toException())
                        }
                    }
                    
                    usersRef.addListenerForSingleValueEvent(listener)
                    
                    continuation.invokeOnCancellation {
                        usersRef.removeEventListener(listener)
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to load users", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Load servers for specific user
     */
    suspend fun loadUserServers(userId: String): Result<List<AdminServerModel>> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_SERVERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }
                
                suspendCancellableCoroutine<List<AdminServerModel>> { continuation ->
                    val serversRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH)
                    
                    val listener = object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            try {
                                val servers = mutableListOf<AdminServerModel>()
                                
                                for (serverSnapshot in snapshot.children) {
                                    val serverId = serverSnapshot.key ?: continue
                                    val serverData = serverSnapshot.getValue(AdminServerModel::class.java)
                                    
                                    if (serverData != null) {
                                        serverData.id = serverId
                                        serverData.userId = userId
                                        servers.add(serverData)
                                    }
                                }
                                
                                // Sort servers by priority and creation date
                                servers.sortWith(compareBy<AdminServerModel> { -it.priority }.thenByDescending { it.createdAt })
                                
                                // Update LiveData
                                _userServers.postValue(servers)
                                
                                android.util.Log.i(AppConfig.TAG, "Loaded ${servers.size} servers for user: $userId")
                                continuation.resume(servers)
                            } catch (e: Exception) {
                                android.util.Log.e(AppConfig.TAG, "Error parsing servers data", e)
                                continuation.resumeWithException(e)
                            }
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            android.util.Log.e(AppConfig.TAG, "Firebase query cancelled: ${error.message}")
                            continuation.resumeWithException(error.toException())
                        }
                    }
                    
                    serversRef.addListenerForSingleValueEvent(listener)
                    
                    continuation.invokeOnCancellation {
                        serversRef.removeEventListener(listener)
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to load user servers", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Update user role
     */
    suspend fun updateUserRole(userId: String, newRole: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_USERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }
                
                // Validate role
                if (newRole !in listOf("user", "admin")) {
                    return@withContext Result.failure(IllegalArgumentException("Invalid role: $newRole"))
                }
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    val userRef = database.child(USERS_PATH).child(userId)
                    
                    val updates = mapOf(
                        "role" to newRole,
                        "updated_at" to System.currentTimeMillis()
                    )
                    
                    userRef.updateChildren(updates)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Updated user role: $userId -> $newRole")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to update user role", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Update user subscription
     */
    suspend fun updateUserSubscription(userId: String, subscriptionEnd: String, subscriptionType: String = "basic"): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_SUBSCRIPTIONS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }
                
                // Validate date format
                if (subscriptionEnd.isNotBlank()) {
                    try {
                        java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).parse(subscriptionEnd)
                    } catch (e: Exception) {
                        return@withContext Result.failure(IllegalArgumentException("Invalid date format. Use yyyy-MM-dd"))
                    }
                }
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    val userRef = database.child(USERS_PATH).child(userId)
                    
                    val updates = mapOf(
                        "subscription_end" to subscriptionEnd,
                        "subscription_type" to subscriptionType,
                        "is_active" to true,
                        "updated_at" to System.currentTimeMillis()
                    )
                    
                    userRef.updateChildren(updates)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Updated user subscription: $userId -> $subscriptionEnd")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to update user subscription", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Delete user
     */
    suspend fun deleteUser(userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("DELETE_USERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }
                
                // Prevent deleting current admin user
                val currentUser = auth.currentUser
                if (currentUser?.uid == userId) {
                    return@withContext Result.failure(IllegalArgumentException("Cannot delete current admin user"))
                }
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    val userRef = database.child(USERS_PATH).child(userId)
                    
                    userRef.removeValue()
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Deleted user: $userId")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to delete user", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Update admin statistics
     */
    private fun updateAdminStats(users: List<AdminUserModel>) {
        val totalUsers = users.size
        val activeUsers = users.count { it.isActive && !it.isExpired }
        val expiredUsers = users.count { it.isExpired }
        val adminUsers = users.count { it.isAdmin() }
        val totalServers = users.sumOf { it.serverCount }
        val activeServers = users.count { it.isActive } // Simplified calculation
        val totalDataUsage = users.sumOf { it.dataUsedGb }
        val averageDataUsage = if (totalUsers > 0) totalDataUsage.toFloat() / totalUsers else 0f
        
        val stats = AdminStats(
            totalUsers = totalUsers,
            activeUsers = activeUsers,
            expiredUsers = expiredUsers,
            adminUsers = adminUsers,
            totalServers = totalServers,
            activeServers = activeServers,
            totalDataUsage = totalDataUsage,
            averageDataUsage = averageDataUsage
        )
        
        _adminStats.postValue(stats)
    }
    
    /**
     * Search users by email or name
     */
    fun searchUsers(query: String): List<AdminUserModel> {
        val allUsersList = _allUsers.value ?: emptyList()
        return if (query.isBlank()) {
            allUsersList
        } else {
            allUsersList.filter { user ->
                user.email.contains(query, ignoreCase = true) ||
                user.name.contains(query, ignoreCase = true) ||
                user.uid.contains(query, ignoreCase = true)
            }
        }
    }
    
    /**
     * Get expired users
     */
    fun getExpiredUsers(): List<AdminUserModel> {
        return _allUsers.value?.filter { it.isExpired } ?: emptyList()
    }
    
    /**
     * Get users expiring soon
     */
    fun getUsersExpiringSoon(): List<AdminUserModel> {
        return _allUsers.value?.filter { it.daysUntilExpiry in 1..7 } ?: emptyList()
    }
    
    /**
     * Refresh all data
     */
    suspend fun refreshData(): Result<Unit> {
        return try {
            loadAllUsers()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Add server to user
     */
    suspend fun addUserServer(userId: String, server: AdminServerModel): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_SERVERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }

                // Validate server data
                if (!server.isValid()) {
                    val errors = server.getValidationErrors()
                    return@withContext Result.failure(IllegalArgumentException("Server validation failed: ${errors.joinToString(", ")}"))
                }

                suspendCancellableCoroutine<Unit> { continuation ->
                    val serverRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH).child(server.id)

                    val serverData = server.copy(
                        userId = userId,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis()
                    )

                    serverRef.setValue(serverData.toFirebaseServerModel())
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Added server to user: $userId -> ${server.id}")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to add server to user", exception)
                            continuation.resumeWithException(exception)
                        }
                }

                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Update user server
     */
    suspend fun updateUserServer(userId: String, server: AdminServerModel): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_SERVERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }

                // Validate server data
                if (!server.isValid()) {
                    val errors = server.getValidationErrors()
                    return@withContext Result.failure(IllegalArgumentException("Server validation failed: ${errors.joinToString(", ")}"))
                }

                suspendCancellableCoroutine<Unit> { continuation ->
                    val serverRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH).child(server.id)

                    val serverData = server.copy(
                        userId = userId,
                        updatedAt = System.currentTimeMillis()
                    )

                    serverRef.setValue(serverData.toFirebaseServerModel())
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Updated server for user: $userId -> ${server.id}")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to update server for user", exception)
                            continuation.resumeWithException(exception)
                        }
                }

                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Delete user server
     */
    suspend fun deleteUserServer(userId: String, serverId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_SERVERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }

                suspendCancellableCoroutine<Unit> { continuation ->
                    val serverRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH).child(serverId)

                    serverRef.removeValue()
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Deleted server from user: $userId -> $serverId")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to delete server from user", exception)
                            continuation.resumeWithException(exception)
                        }
                }

                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Toggle server status
     */
    suspend fun toggleServerStatus(userId: String, serverId: String, isActive: Boolean): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!roleBasedAccessControl.hasPermission("MANAGE_SERVERS")) {
                    return@withContext Result.failure(SecurityException("Access denied: Admin role required"))
                }

                suspendCancellableCoroutine<Unit> { continuation ->
                    val serverRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH).child(serverId)

                    val updates = mapOf(
                        "is_active" to isActive,
                        "updated_at" to System.currentTimeMillis()
                    )

                    serverRef.updateChildren(updates)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Toggled server status: $userId -> $serverId -> $isActive")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to toggle server status", exception)
                            continuation.resumeWithException(exception)
                        }
                }

                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Clear cached data
     */
    fun clearData() {
        _allUsers.postValue(emptyList())
        _userServers.postValue(emptyList())
        _adminStats.postValue(AdminStats())
        android.util.Log.i(AppConfig.TAG, "Admin management data cleared")
    }
}
