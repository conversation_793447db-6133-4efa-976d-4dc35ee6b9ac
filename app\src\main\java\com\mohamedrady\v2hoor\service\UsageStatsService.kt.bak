package com.mohamedrady.v2hoor.service

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.UsageSession
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Usage Statistics Service
 * Tracks and manages VPN usage statistics
 */
class UsageStatsService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: UsageStatsService? = null
        
        fun getInstance(context: Context): UsageStatsService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UsageStatsService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val STATS_PATH = "stats"
        private const val SESSIONS_PATH = "sessions"
        private const val DAILY_STATS_PATH = "daily_stats"
        private const val MONTHLY_STATS_PATH = "monthly_stats"
    }
    
    data class UsageStats(
        val totalSessions: Int = 0,
        val totalDuration: Long = 0, // seconds
        val totalDataUsage: Long = 0, // bytes
        val averageSessionDuration: Long = 0, // seconds
        val averageDataPerSession: Long = 0, // bytes
        val mostUsedServer: String = "",
        val mostUsedConnectionType: String = "",
        val todaySessions: Int = 0,
        val todayDuration: Long = 0,
        val todayDataUsage: Long = 0,
        val thisWeekSessions: Int = 0,
        val thisWeekDuration: Long = 0,
        val thisWeekDataUsage: Long = 0,
        val thisMonthSessions: Int = 0,
        val thisMonthDuration: Long = 0,
        val thisMonthDataUsage: Long = 0
    )
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    
    // LiveData for usage statistics
    private val _usageStats = MutableLiveData<UsageStats>()
    val usageStats: LiveData<UsageStats> = _usageStats
    
    private val _recentSessions = MutableLiveData<List<UsageSession>>()
    val recentSessions: LiveData<List<UsageSession>> = _recentSessions
    
    private val _currentSession = MutableLiveData<UsageSession?>()
    val currentSession: LiveData<UsageSession?> = _currentSession
    
    // Current active session
    private var activeSession: UsageSession? = null
    
    /**
     * Start a new usage session
     */
    suspend fun startSession(serverId: String, serverName: String, serverLocation: String = ""): Result<UsageSession> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    return@withContext Result.failure(IllegalStateException("User not authenticated"))
                }
                
                // End any existing active session
                activeSession?.let { endSession(it.sessionId, "new_session") }
                
                // Create new session
                val connectionType = getConnectionType()
                val session = UsageSession.createSession(
                    userId = currentUser.uid,
                    serverId = serverId,
                    serverName = serverName,
                    serverLocation = serverLocation,
                    connectionType = connectionType
                )
                
                // Save session to Firebase
                suspendCancellableCoroutine<Unit> { continuation ->
                    val sessionRef = database.child("users").child(currentUser.uid)
                        .child(STATS_PATH).child(SESSIONS_PATH).child(session.sessionId)
                    
                    sessionRef.setValue(session)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Session started: ${session.sessionId}")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to start session", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                
                // Set as active session
                activeSession = session
                _currentSession.postValue(session)
                
                android.util.Log.i(AppConfig.TAG, "Usage session started: ${session.sessionId}")
                Result.success(session)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to start usage session", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * End a usage session
     */
    suspend fun endSession(sessionId: String, reason: String = "user"): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    return@withContext Result.failure(IllegalStateException("User not authenticated"))
                }
                
                // Update session in Firebase
                suspendCancellableCoroutine<Unit> { continuation ->
                    val sessionRef = database.child("users").child(currentUser.uid)
                        .child(STATS_PATH).child(SESSIONS_PATH).child(sessionId)
                    
                    val updates = mapOf(
                        "end_time" to System.currentTimeMillis(),
                        "duration_seconds" to if (activeSession?.sessionId == sessionId) {
                            activeSession?.getDurationSeconds() ?: 0
                        } else 0,
                        "disconnect_reason" to reason,
                        "updated_at" to System.currentTimeMillis()
                    )
                    
                    sessionRef.updateChildren(updates)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Session ended: $sessionId")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to end session", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                
                // Clear active session if it matches
                if (activeSession?.sessionId == sessionId) {
                    activeSession?.endSession(reason)
                    activeSession = null
                    _currentSession.postValue(null)
                }
                
                // Refresh statistics
                loadUsageStats()
                
                android.util.Log.i(AppConfig.TAG, "Usage session ended: $sessionId")
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to end usage session", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Update session data usage
     */
    suspend fun updateSessionDataUsage(sessionId: String, bytesSent: Long, bytesReceived: Long): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    return@withContext Result.failure(IllegalStateException("User not authenticated"))
                }
                
                // Update session in Firebase
                val sessionRef = database.child("users").child(currentUser.uid)
                    .child(STATS_PATH).child(SESSIONS_PATH).child(sessionId)
                
                val updates = mapOf(
                    "bytes_sent" to bytesSent,
                    "bytes_received" to bytesReceived,
                    "updated_at" to System.currentTimeMillis()
                )
                
                sessionRef.updateChildren(updates)
                
                // Update active session if it matches
                if (activeSession?.sessionId == sessionId) {
                    activeSession?.bytesSent = bytesSent
                    activeSession?.bytesReceived = bytesReceived
                    activeSession?.updatedAt = System.currentTimeMillis()
                    _currentSession.postValue(activeSession)
                }
                
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to update session data usage", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Load usage statistics
     */
    suspend fun loadUsageStats(): Result<UsageStats> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    return@withContext Result.failure(IllegalStateException("User not authenticated"))
                }
                
                suspendCancellableCoroutine<UsageStats> { continuation ->
                    val statsRef = database.child("users").child(currentUser.uid).child(STATS_PATH).child(SESSIONS_PATH)
                    
                    statsRef.addListenerForSingleValueEvent(object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            try {
                                val sessions = mutableListOf<UsageSession>()
                                
                                for (sessionSnapshot in snapshot.children) {
                                    val session = sessionSnapshot.getValue(UsageSession::class.java)
                                    if (session != null) {
                                        sessions.add(session)
                                    }
                                }
                                
                                val stats = calculateStats(sessions)
                                _usageStats.postValue(stats)
                                _recentSessions.postValue(sessions.sortedByDescending { it.startTime }.take(20))
                                
                                android.util.Log.i(AppConfig.TAG, "Usage stats loaded: ${sessions.size} sessions")
                                continuation.resume(stats)
                            } catch (e: Exception) {
                                android.util.Log.e(AppConfig.TAG, "Error processing usage stats", e)
                                continuation.resumeWithException(e)
                            }
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            android.util.Log.e(AppConfig.TAG, "Failed to load usage stats: ${error.message}")
                            continuation.resumeWithException(error.toException())
                        }
                    })
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to load usage statistics", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Calculate statistics from sessions
     */
    private fun calculateStats(sessions: List<UsageSession>): UsageStats {
        if (sessions.isEmpty()) {
            return UsageStats()
        }
        
        val totalSessions = sessions.size
        val totalDuration = sessions.sumOf { it.getDurationSeconds() }
        val totalDataUsage = sessions.sumOf { it.getTotalBytes() }
        
        val todaySessions = sessions.filter { it.isFromToday() }
        val thisWeekSessions = sessions.filter { it.isFromThisWeek() }
        val thisMonthSessions = sessions.filter { it.isFromThisMonth() }
        
        val serverUsage = sessions.groupBy { it.serverName }
            .mapValues { it.value.size }
            .maxByOrNull { it.value }
        
        val connectionTypeUsage = sessions.groupBy { it.connectionType }
            .mapValues { it.value.size }
            .maxByOrNull { it.value }
        
        return UsageStats(
            totalSessions = totalSessions,
            totalDuration = totalDuration,
            totalDataUsage = totalDataUsage,
            averageSessionDuration = if (totalSessions > 0) totalDuration / totalSessions else 0,
            averageDataPerSession = if (totalSessions > 0) totalDataUsage / totalSessions else 0,
            mostUsedServer = serverUsage?.key ?: "",
            mostUsedConnectionType = connectionTypeUsage?.key ?: "",
            todaySessions = todaySessions.size,
            todayDuration = todaySessions.sumOf { it.getDurationSeconds() },
            todayDataUsage = todaySessions.sumOf { it.getTotalBytes() },
            thisWeekSessions = thisWeekSessions.size,
            thisWeekDuration = thisWeekSessions.sumOf { it.getDurationSeconds() },
            thisWeekDataUsage = thisWeekSessions.sumOf { it.getTotalBytes() },
            thisMonthSessions = thisMonthSessions.size,
            thisMonthDuration = thisMonthSessions.sumOf { it.getDurationSeconds() },
            thisMonthDataUsage = thisMonthSessions.sumOf { it.getTotalBytes() }
        )
    }
    
    /**
     * Get connection type
     */
    private fun getConnectionType(): String {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) 
                as android.net.ConnectivityManager
            val activeNetwork = connectivityManager.activeNetworkInfo
            
            when (activeNetwork?.type) {
                android.net.ConnectivityManager.TYPE_WIFI -> "wifi"
                android.net.ConnectivityManager.TYPE_MOBILE -> "mobile"
                android.net.ConnectivityManager.TYPE_ETHERNET -> "ethernet"
                else -> "unknown"
            }
        } catch (e: Exception) {
            "unknown"
        }
    }
    
    /**
     * Get current active session
     */
    fun getCurrentSession(): UsageSession? {
        return activeSession
    }
    
    /**
     * Check if there's an active session
     */
    fun hasActiveSession(): Boolean {
        return activeSession != null && activeSession?.isActive() == true
    }
    
    /**
     * Clear all usage data
     */
    suspend fun clearAllUsageData(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    return@withContext Result.failure(IllegalStateException("User not authenticated"))
                }
                
                val statsRef = database.child("users").child(currentUser.uid).child(STATS_PATH)
                statsRef.removeValue()
                
                // Clear local data
                activeSession = null
                _currentSession.postValue(null)
                _usageStats.postValue(UsageStats())
                _recentSessions.postValue(emptyList())
                
                android.util.Log.i(AppConfig.TAG, "All usage data cleared")
                Result.success(Unit)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to clear usage data", e)
                Result.failure(e)
            }
        }
    }
}
