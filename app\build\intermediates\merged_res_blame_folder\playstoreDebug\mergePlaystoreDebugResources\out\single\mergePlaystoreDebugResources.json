[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_settings_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_settings_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_log_entry.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_log_entry.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_privacy_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_privacy_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_nav_header_bg.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\nav_header_bg.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_none.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_none.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_per_apps_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_per_apps_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxxhdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxxhdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_admin_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_admin_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_recycler_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_recycler_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_google.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_google.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_subscriptions_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_subscriptions_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_recycler_routing_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_recycler_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_add_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_add_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxxhdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxxhdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_share_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_share_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_user_default.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_user_default.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_nav_header_bg.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\nav_header_bg.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xhdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xhdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_edit_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_edit_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xhdpi_ic_banner.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xhdpi\\ic_banner.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_privacy_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_privacy_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_add_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_add_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_feedback_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_feedback_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_logcat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_bg_status_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\bg_status_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-hdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-hdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xhdpi_ic_banner_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xhdpi\\ic_banner_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_backup_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_backup_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_qu_switch_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_qu_switch_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_visibility.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_visibility.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_promotion_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_promotion_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_license_24px.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\license_24px.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_user_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_user_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_select_all.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_select_all.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-hdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-hdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_check_update.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_check_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_visibility_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_visibility_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_user_avatar_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\user_avatar_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_user_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_user_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-mdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-mdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_routing_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_routing_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_play_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_play_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_real_time_log.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_real_time_log.xml"}, {"merged": "com.mohamedrady.v2hoor.app-playstoreDebug-75:/drawable_ic_close_24dp.xml.flat", "source": "com.mohamedrady.v2hoor.app-main-77:/drawable/ic_close_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_add_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_add_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_recycler_user_asset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_recycler_user_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_tasker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_tasker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_action_sub_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\action_sub_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_promote_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_promote_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_server_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_server_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_subscription_active.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_subscription_active.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_routing_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_people.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_people.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_custom_divider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\custom_divider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_widget_switch.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\widget_switch.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_user_asset_url.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_user_asset_url.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_admin_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_admin_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_admin_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_admin_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_restore_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_restore_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_play_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_play_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_file_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_file_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_rounded_corner_inactive.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_rounded_corner_inactive.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_sub_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_sub_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_share_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_share_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_admin_users.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_admin_users.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_drawer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_drawer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_recycler_logcat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_recycler_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_layout_address_port.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\layout_address_port.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_select_all_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_select_all_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_hysteria2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_hysteria2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_scan_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_scan_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\xml_app_widget_provider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\xml\\app_widget_provider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_action_done.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_action_done.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_bg_button_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\bg_button_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-mdpi\\ic_launcher_foreground.png"}, {"merged": "com.mohamedrady.v2hoor.app-playstoreDebug-75:/drawable_ic_arrow_back_24dp.xml.flat", "source": "com.mohamedrady.v2hoor.app-main-77:/drawable/ic_arrow_back_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_restore_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_restore_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_recycler_footer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_recycler_footer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_preference_with_help_link.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\preference_with_help_link.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_save_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_save_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_subscriptions_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_subscriptions_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_user_asset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_user_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_telegram_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_telegram_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_vless.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_vless.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_system_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_system_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_login_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\login_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_about.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_light_mode_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_light_mode_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_subscription_warning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_subscription_warning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_star.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_star.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_delete_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_delete_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\font_montserrat_thin.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\font\\montserrat_thin.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_custom_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_custom_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_subscription_expired.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_subscription_expired.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_theme_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_theme_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_google_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\google_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_admin_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_admin_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_user_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_user_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xxhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_settings_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_settings_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_lock_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_lock_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-mdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-mdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_login_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\login_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_outline_filter_alt_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_outline_filter_alt_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_outline_filter_alt_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_outline_filter_alt_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_clear_all.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_clear_all.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xhdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xhdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xxxhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_qrcode.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_qrcode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_description_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_description_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-mdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-mdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_cloud_download_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_cloud_download_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_feedback_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_feedback_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-mdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-mdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_sub_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_sub_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_scanner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_bg_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\bg_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_backup_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_backup_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_users.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_users.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_upload.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_upload.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-anydpi-v26_ic_banner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-anydpi-v26\\ic_banner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_telegram_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_telegram_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_scan_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_scan_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_stop_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_stop_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_bg_button_danger.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\bg_button_danger.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_wireguard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_wireguard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_subscription_action_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\subscription_action_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_log_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_log_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_fab_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_fab_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_recycler_sub_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_recycler_sub_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_more_vert.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_more_vert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_file_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_file_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_bypass_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_bg_role_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\bg_role_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_routing_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_routing_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_recycler_bypass_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_recycler_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_dialog_edit_subscription.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\dialog_edit_subscription.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_user_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_user_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_save_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_save_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxhdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxhdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_action_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\action_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_lock_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_lock_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_image_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_image_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_bypass_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_about_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_about_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_cloud_download_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_cloud_download_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_layout_tls.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\layout_tls.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_per_apps_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_per_apps_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_check_update_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_check_update_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_admin_users.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_admin_users.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_real_time_log.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_real_time_log.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_about_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_about_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_admin_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_admin_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_dark_mode_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_dark_mode_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\raw_licenses.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\raw\\licenses.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_more_vert_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_more_vert_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-hdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-hdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_socks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_socks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_description_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_description_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_action_done.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_action_done.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_image_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_image_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_logs.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_logs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_logcat_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_logcat_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_source_code_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_source_code_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_delete_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_delete_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_routing_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_fab_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_fab_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_vmess.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_vmess.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_bg_button_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\bg_button_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_promotion_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_promotion_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_bar_chart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_bar_chart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxhdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxhdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_dialog_config_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\dialog_config_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_stop_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_stop_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_arrow_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_arrow_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_item_admin_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\item_admin_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xhdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xhdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_edit_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_edit_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_qu_scan_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_qu_scan_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\xml_cache_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\xml\\cache_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_logcat_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_logcat_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_edit_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_edit_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxhdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxhdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxxhdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxxhdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xhdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xhdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_shadowsocks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_shadowsocks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_layout_tls_hysteria2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\layout_tls_hysteria2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_routing_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_routing_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\xml_shortcuts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\xml\\shortcuts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_server_trojan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_server_trojan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_layout_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\layout_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-mdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-mdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_user_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_user_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_more_vert_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_more_vert_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_source_code_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_source_code_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxxhdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxxhdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_logcat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_select_all_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_select_all_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\mipmap-xhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\mipmap-xhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_check_update_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_check_update_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_activity_user_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\activity_user_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\layout_subscription_status_banner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\layout\\subscription_status_banner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_ic_rounded_corner_active.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\ic_rounded_corner_active.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\color_color_highlight_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\color\\color_highlight_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable_subscription_banner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable\\subscription_banner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\menu_menu_asset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\menu\\menu_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-xxhdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-xxhdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\drawable-night_ic_add_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\drawable-night\\ic_add_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-playstoreDebug-75:\\xml_pref_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-77:\\xml\\pref_settings.xml"}]