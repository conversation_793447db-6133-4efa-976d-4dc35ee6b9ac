%androidx.multidex.MultiDexApplicationandroid.os.Parcelablekotlin.Enum"com.mohamedrady.v2hoor.fmt.FmtBase8androidx.recyclerview.widget.RecyclerView.ItemDecoration5androidx.recyclerview.widget.ItemTouchHelper.Callback,com.mohamedrady.v2hoor.plugin.ResolvedPluginjava.util.ArrayListjava.io.FileNotFoundException$com.mohamedrady.v2hoor.plugin.Plugin!android.content.BroadcastReceiver#android.appwidget.AppWidgetProviderandroidx.work.CoroutineWorker)android.service.quicksettings.TileServiceandroid.app.Service-com.mohamedrady.v2hoor.service.ServiceControllibv2ray.CoreCallbackHandlerandroid.net.VpnService&com.mohamedrady.v2hoor.ui.BaseActivity(androidx.appcompat.app.AppCompatActivity0androidx.viewpager2.adapter.FragmentStateAdapterGandroidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderVcom.google.android.material.navigation.NavigationView.OnNavigationItemSelectedListener4com.mohamedrady.v2hoor.helper.ItemTouchHelperAdapter<com.mohamedrady.v2hoor.ui.MainRecyclerAdapter.BaseViewHolder7com.mohamedrady.v2hoor.helper.ItemTouchHelperViewHolder;com.mohamedrady.v2hoor.ui.PerAppProxyAdapter.BaseViewHolder!android.view.View.OnClickListenerFcom.mohamedrady.v2hoor.ui.RoutingSettingRecyclerAdapter.BaseViewHolder,androidx.preference.PreferenceFragmentCompatBcom.mohamedrady.v2hoor.ui.SubSettingRecyclerAdapter.BaseViewHolderandroid.widget.LinearLayout(androidx.recyclerview.widget.ListAdapter2androidx.recyclerview.widget.DiffUtil.ItemCallback)java.lang.Thread.UncaughtExceptionHandlerandroid.content.ContextWrapperandroidx.lifecycle.ViewModel#androidx.lifecycle.AndroidViewModelBandroid.content.SharedPreferences.OnSharedPreferenceChangeListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   