{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergePlaystoreDebugResources-74:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\881daab11df0f8fb6be0bbb2034fa98e\\transformed\\material-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1034,1098,1193,1263,1326,1433,1498,1565,1626,1693,1755,1809,1923,1982,2043,2097,2172,2298,2386,2472,2573,2663,2753,2895,2967,3040,3177,3266,3347,3404,3460,3526,3597,3674,3745,3825,3897,3973,4054,4124,4224,4311,4383,4474,4567,4641,4716,4808,4860,4942,5008,5092,5178,5240,5304,5367,5436,5540,5644,5738,5838,5899,5959,6043,6127,6203", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "268,346,422,501,595,683,775,887,969,1029,1093,1188,1258,1321,1428,1493,1560,1621,1688,1750,1804,1918,1977,2038,2092,2167,2293,2381,2467,2568,2658,2748,2890,2962,3035,3172,3261,3342,3399,3455,3521,3592,3669,3740,3820,3892,3968,4049,4119,4219,4306,4378,4469,4562,4636,4711,4803,4855,4937,5003,5087,5173,5235,5299,5362,5431,5535,5639,5733,5833,5894,5954,6038,6122,6198,6277"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,72,73,75,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,150,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3244,3322,3398,3477,3571,4378,4470,4582,7256,7316,7466,9172,9242,9305,9412,9477,9544,9605,9672,9734,9788,9902,9961,10022,10076,10151,10277,10365,10451,10552,10642,10732,10874,10946,11019,11156,11245,11326,11383,11439,11505,11576,11653,11724,11804,11876,11952,12033,12103,12203,12290,12362,12453,12546,12620,12695,12787,12839,12921,12987,13071,13157,13219,13283,13346,13415,13519,13623,13717,13817,13878,14017,14460,14544,14620", "endLines": "5,35,36,37,38,39,47,48,49,72,73,75,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,150,155,156,157", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "318,3317,3393,3472,3566,3654,4465,4577,4659,7311,7375,7556,9237,9300,9407,9472,9539,9600,9667,9729,9783,9897,9956,10017,10071,10146,10272,10360,10446,10547,10637,10727,10869,10941,11014,11151,11240,11321,11378,11434,11500,11571,11648,11719,11799,11871,11947,12028,12098,12198,12285,12357,12448,12541,12615,12690,12782,12834,12916,12982,13066,13152,13214,13278,13341,13410,13514,13618,13712,13812,13873,13933,14096,14539,14615,14694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\004c9aef0bfe597de4245d1fd9384d65\\transformed\\credentials-1.2.0-rc01\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,116", "endOffsets": "163,280"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3014,3127", "endColumns": "112,116", "endOffsets": "3122,3239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ba00bb078231f50d815e7c2c79fbd77c\\transformed\\browser-1.4.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "70,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "7066,7561,7665,7773", "endColumns": "99,103,107,104", "endOffsets": "7161,7660,7768,7873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f41aa919bb31a17aa092171265719b55\\transformed\\biometric-1.1.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,260,384,513,646,782,901,1040,1135,1284,1426", "endColumns": "114,89,123,128,132,135,118,138,94,148,141,127", "endOffsets": "165,255,379,508,641,777,896,1035,1130,1279,1421,1549"}, "to": {"startLines": "68,71,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6881,7166,7878,8002,8131,8264,8400,8519,8658,8753,8902,9044", "endColumns": "114,89,123,128,132,135,118,138,94,148,141,127", "endOffsets": "6991,7251,7997,8126,8259,8395,8514,8653,8748,8897,9039,9167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17d5b14458b73464e26d2134afde20b1\\transformed\\play-services-base-18.5.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4664,4767,4920,5046,5152,5292,5418,5541,5814,5979,6085,6242,6371,6524,6681,6744,6803", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4762,4915,5041,5147,5287,5413,5536,5645,5974,6080,6237,6366,6519,6676,6739,6798,6876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d304b09fc6c5965909d0ef57a0fa0ff6\\transformed\\core-1.16.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,359,462,566,663,774", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "150,252,354,457,561,658,769,870"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3659,3759,3861,3963,4066,4170,4267,14699", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "3754,3856,3958,4061,4165,4262,4373,14795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e9c4fffcc61ffb5e8382deeedde39b2\\transformed\\appcompat-1.7.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,14378", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,14455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4578899047b14a577ffdc3b874f24398\\transformed\\play-services-basement-18.5.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5650", "endColumns": "163", "endOffsets": "5809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\65324aff12f16468e03fc5b512ae58fc\\transformed\\preference-1.2.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,340,483,652,736", "endColumns": "69,85,78,142,168,83,79", "endOffsets": "170,256,335,478,647,731,811"}, "to": {"startLines": "69,74,149,151,159,160,161", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6996,7380,13938,14101,14800,14969,15053", "endColumns": "69,85,78,142,168,83,79", "endOffsets": "7061,7461,14012,14239,14964,15048,15128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\76702c707d43210cfa7dee4ec2c1641f\\transformed\\quickie-foss-1.14.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,119", "endColumns": "63,69", "endOffsets": "114,184"}, "to": {"startLines": "152,153", "startColumns": "4,4", "startOffsets": "14244,14308", "endColumns": "63,69", "endOffsets": "14303,14373"}}]}]}