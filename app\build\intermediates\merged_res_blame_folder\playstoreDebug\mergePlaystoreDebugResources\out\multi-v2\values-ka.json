{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergePlaystoreDebugResources-74:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d304b09fc6c5965909d0ef57a0fa0ff6\\transformed\\core-1.16.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3682,3778,3880,3979,4078,4184,4288,14844", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3773,3875,3974,4073,4179,4283,4401,14940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17d5b14458b73464e26d2134afde20b1\\transformed\\play-services-base-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4718,4823,4972,5100,5210,5364,5498,5620,5871,6044,6152,6307,6435,6596,6735,6801,6862", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4818,4967,5095,5205,5359,5493,5615,5723,6039,6147,6302,6430,6591,6730,6796,6857,6933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\65324aff12f16468e03fc5b512ae58fc\\transformed\\preference-1.2.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,350,490,659,745", "endColumns": "71,87,84,139,168,85,80", "endOffsets": "172,260,345,485,654,740,821"}, "to": {"startLines": "69,74,149,151,159,160,161", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7050,7449,14073,14238,14945,15114,15200", "endColumns": "71,87,84,139,168,85,80", "endOffsets": "7117,7532,14153,14373,15109,15195,15276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4578899047b14a577ffdc3b874f24398\\transformed\\play-services-basement-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5728", "endColumns": "142", "endOffsets": "5866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\76702c707d43210cfa7dee4ec2c1641f\\transformed\\quickie-foss-1.14.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,123", "endColumns": "67,69", "endOffsets": "118,188"}, "to": {"startLines": "152,153", "startColumns": "4,4", "startOffsets": "14378,14446", "endColumns": "67,69", "endOffsets": "14441,14511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\004c9aef0bfe597de4245d1fd9384d65\\transformed\\credentials-1.2.0-rc01\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,122", "endOffsets": "160,283"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3026,3136", "endColumns": "109,122", "endOffsets": "3131,3254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f41aa919bb31a17aa092171265719b55\\transformed\\biometric-1.1.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,258,379,512,651,793,926,1064,1168,1320,1470", "endColumns": "111,90,120,132,138,141,132,137,103,151,149,120", "endOffsets": "162,253,374,507,646,788,921,1059,1163,1315,1465,1586"}, "to": {"startLines": "68,71,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6938,7228,7952,8073,8206,8345,8487,8620,8758,8862,9014,9164", "endColumns": "111,90,120,132,138,141,132,137,103,151,149,120", "endOffsets": "7045,7314,8068,8201,8340,8482,8615,8753,8857,9009,9159,9280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\881daab11df0f8fb6be0bbb2034fa98e\\transformed\\material-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1071,1138,1235,1304,1367,1454,1518,1584,1644,1713,1774,1828,1943,2002,2062,2116,2188,2318,2406,2485,2583,2671,2755,2893,2971,3047,3186,3280,3360,3416,3470,3536,3609,3687,3758,3842,3915,3993,4066,4141,4251,4341,4416,4510,4608,4682,4759,4859,4912,4996,5064,5153,5242,5304,5369,5432,5502,5609,5709,5809,5905,5965,6023,6103,6193,6268", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "268,346,420,504,597,691,790,915,1003,1066,1133,1230,1299,1362,1449,1513,1579,1639,1708,1769,1823,1938,1997,2057,2111,2183,2313,2401,2480,2578,2666,2750,2888,2966,3042,3181,3275,3355,3411,3465,3531,3604,3682,3753,3837,3910,3988,4061,4136,4246,4336,4411,4505,4603,4677,4754,4854,4907,4991,5059,5148,5237,5299,5364,5427,5497,5604,5704,5804,5900,5960,6018,6098,6188,6263,6344"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,72,73,75,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,150,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3259,3337,3411,3495,3588,4406,4505,4630,7319,7382,7537,9285,9354,9417,9504,9568,9634,9694,9763,9824,9878,9993,10052,10112,10166,10238,10368,10456,10535,10633,10721,10805,10943,11021,11097,11236,11330,11410,11466,11520,11586,11659,11737,11808,11892,11965,12043,12116,12191,12301,12391,12466,12560,12658,12732,12809,12909,12962,13046,13114,13203,13292,13354,13419,13482,13552,13659,13759,13859,13955,14015,14158,14598,14688,14763", "endLines": "5,35,36,37,38,39,47,48,49,72,73,75,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,150,155,156,157", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "318,3332,3406,3490,3583,3677,4500,4625,4713,7377,7444,7629,9349,9412,9499,9563,9629,9689,9758,9819,9873,9988,10047,10107,10161,10233,10363,10451,10530,10628,10716,10800,10938,11016,11092,11231,11325,11405,11461,11515,11581,11654,11732,11803,11887,11960,12038,12111,12186,12296,12386,12461,12555,12653,12727,12804,12904,12957,13041,13109,13198,13287,13349,13414,13477,13547,13654,13754,13854,13950,14010,14068,14233,14683,14758,14839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ba00bb078231f50d815e7c2c79fbd77c\\transformed\\browser-1.4.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "70,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "7122,7634,7737,7847", "endColumns": "105,102,109,104", "endOffsets": "7223,7732,7842,7947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e9c4fffcc61ffb5e8382deeedde39b2\\transformed\\appcompat-1.7.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,14516", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,14593"}}]}]}