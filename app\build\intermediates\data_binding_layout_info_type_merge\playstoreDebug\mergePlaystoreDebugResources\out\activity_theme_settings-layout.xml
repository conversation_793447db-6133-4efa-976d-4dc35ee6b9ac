<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_theme_settings" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_theme_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_theme_settings_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="361" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="24" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="63"/></Target><Target id="@+id/iv_theme_indicator" view="ImageView"><Expressions/><location startLine="52" startOffset="20" endLine="58" endOffset="59"/></Target><Target id="@+id/tv_current_theme" view="TextView"><Expressions/><location startLine="66" startOffset="24" endLine="73" endOffset="70"/></Target><Target id="@+id/tv_theme_status" view="TextView"><Expressions/><location startLine="75" startOffset="24" endLine="81" endOffset="77"/></Target><Target id="@+id/btn_toggle_theme" view="Button"><Expressions/><location startLine="85" startOffset="20" endLine="91" endOffset="79"/></Target><Target id="@+id/spinner_theme_mode" view="Spinner"><Expressions/><location startLine="120" startOffset="20" endLine="124" endOffset="60"/></Target><Target id="@+id/btn_preview_light" view="Button"><Expressions/><location startLine="132" startOffset="24" endLine="140" endOffset="83"/></Target><Target id="@+id/btn_preview_dark" view="Button"><Expressions/><location startLine="142" startOffset="24" endLine="150" endOffset="83"/></Target><Target id="@+id/switch_auto_schedule" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="188" startOffset="24" endLine="191" endOffset="66"/></Target><Target id="@+id/layout_schedule_settings" view="LinearLayout"><Expressions/><location startLine="204" startOffset="20" endLine="280" endOffset="34"/></Target><Target id="@+id/btn_schedule_start" view="Button"><Expressions/><location startLine="218" startOffset="28" endLine="226" endOffset="87"/></Target><Target id="@+id/btn_schedule_end" view="Button"><Expressions/><location startLine="228" startOffset="28" endLine="236" endOffset="87"/></Target><Target id="@+id/tv_schedule_start" view="TextView"><Expressions/><location startLine="253" startOffset="28" endLine="260" endOffset="72"/></Target><Target id="@+id/tv_schedule_end" view="TextView"><Expressions/><location startLine="269" startOffset="28" endLine="276" endOffset="72"/></Target><Target id="@+id/tv_stats_current_theme" view="TextView"><Expressions/><location startLine="309" startOffset="20" endLine="316" endOffset="59"/></Target><Target id="@+id/tv_stats_dark_active" view="TextView"><Expressions/><location startLine="318" startOffset="20" endLine="325" endOffset="59"/></Target><Target id="@+id/tv_stats_auto_schedule" view="TextView"><Expressions/><location startLine="327" startOffset="20" endLine="334" endOffset="59"/></Target><Target id="@+id/tv_stats_schedule_time" view="TextView"><Expressions/><location startLine="336" startOffset="20" endLine="342" endOffset="73"/></Target><Target id="@+id/btn_reset_theme" view="Button"><Expressions/><location startLine="349" startOffset="12" endLine="355" endOffset="71"/></Target></Targets></Layout>