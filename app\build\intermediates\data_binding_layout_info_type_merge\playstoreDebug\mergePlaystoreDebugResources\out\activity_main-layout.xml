<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="43"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="17" startOffset="12" endLine="20" endOffset="61"/></Target><Target id="@+id/main_content" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="28" startOffset="12" endLine="118" endOffset="65"/></Target><Target id="@+id/pb_waiting" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="38" startOffset="20" endLine="44" endOffset="70"/></Target><Target id="@+id/subscription_banner" view="com.mohamedrady.v2hoor.ui.SubscriptionStatusBanner"><Expressions/><location startLine="47" startOffset="20" endLine="51" endOffset="53"/></Target><Target id="@+id/tab_group" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="53" startOffset="20" endLine="60" endOffset="75"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="62" startOffset="20" endLine="68" endOffset="55"/></Target><Target id="@+id/layout_test" view="LinearLayout"><Expressions/><location startLine="70" startOffset="20" endLine="96" endOffset="34"/></Target><Target id="@+id/tv_test_state" view="TextView"><Expressions/><location startLine="85" startOffset="24" endLine="94" endOffset="92"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="105" startOffset="20" endLine="115" endOffset="69"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="122" startOffset="4" endLine="131" endOffset="59"/></Target></Targets></Layout>