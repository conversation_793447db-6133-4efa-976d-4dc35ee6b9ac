1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mohamedrady.v2hoor"
4    android:versionCode="4000657"
5    android:versionName="1.10.7" >
6
7    <uses-sdk
7-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
8        android:minSdkVersion="23"
8-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
9        android:targetSdkVersion="35" />
10
11    <supports-screens
11-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
12        android:anyDensity="true"
12-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
13        android:largeScreens="true"
13-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
14        android:normalScreens="true"
14-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
15        android:smallScreens="true"
15-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
16        android:xlargeScreens="true" />
16-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
17
18    <uses-feature
18-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
19        android:name="android.hardware.camera"
19-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
20        android:required="false" />
20-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
21    <uses-feature
21-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
22        android:name="android.hardware.camera.autofocus"
22-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
23        android:required="false" />
23-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
24    <uses-feature
24-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
25        android:name="android.software.leanback"
25-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
26        android:required="false" />
26-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
27    <uses-feature
27-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
28        android:name="android.hardware.touchscreen"
28-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
29        android:required="false" />
29-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
30
31    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
32    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
34    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.INTERNET" />
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
36    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
37    <uses-permission android:name="android.permission.CAMERA" />
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
38    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
39    <uses-permission
39-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
40        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
40-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
41        android:minSdkVersion="34" />
41-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
42    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
44    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:5-68
46-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:22-65
47    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
48    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
48-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
48-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f41aa919bb31a17aa092171265719b55\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
49    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
49-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
49-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
50    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
51    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
51-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
51-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
52    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
52-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
52-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
53    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
53-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
53-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
54
55    <permission
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
56        android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
56-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
57        android:protectionLevel="signature" />
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
58
59    <uses-permission android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
59-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
59-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
60
61    <uses-feature
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
62        android:name="android.hardware.camera.front"
62-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
63        android:required="false" />
63-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
64    <uses-feature
64-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
65        android:name="android.hardware.camera.flash"
65-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
66        android:required="false" />
66-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
67    <uses-feature
67-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
68        android:name="android.hardware.screen.landscape"
68-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
69        android:required="false" />
69-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
70    <uses-feature
70-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
71        android:name="android.hardware.wifi"
71-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
72        android:required="false" />
72-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
73
74    <application
74-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:5-343:19
75        android:name="com.mohamedrady.v2hoor.AngApplication"
75-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-39
76        android:allowBackup="true"
76-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:banner="@mipmap/ic_banner"
78-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
79        android:debuggable="true"
80        android:extractNativeLibs="true"
81        android:icon="@mipmap/ic_launcher"
81-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-43
82        android:label="@string/app_name"
82-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-41
83        android:networkSecurityConfig="@xml/network_security_config"
83-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-69
84        android:supportsRtl="true"
84-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-35
85        android:theme="@style/AppThemeDayNight"
85-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-48
86        android:usesCleartextTraffic="true" >
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:58:9-44
87        <activity
87-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:9-79:20
88            android:name="com.mohamedrady.v2hoor.ui.MainActivity"
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-44
89            android:exported="true"
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-36
90            android:launchMode="singleTask"
90-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-44
91            android:theme="@style/AppThemeDayNight.NoActionBar" >
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-64
92            <intent-filter>
92-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:13-71:29
93                <action android:name="android.intent.action.MAIN" />
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:17-69
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:25-66
94
95                <category android:name="android.intent.category.LAUNCHER" />
95-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-77
95-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-74
96                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
96-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:17-86
96-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:27-83
97            </intent-filter>
98            <intent-filter>
98-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:13-74:29
99                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
99-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:17-99
99-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:25-96
100            </intent-filter>
101
102            <meta-data
102-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:13-78:53
103                android:name="android.app.shortcuts"
103-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-53
104                android:resource="@xml/shortcuts" />
104-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:78:17-50
105        </activity>
106        <activity
106-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:9-83:60
107            android:name="com.mohamedrady.v2hoor.ui.ServerActivity"
107-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-46
108            android:exported="false"
108-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-37
109            android:windowSoftInputMode="stateUnchanged" />
109-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:13-57
110        <activity
110-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:9-87:60
111            android:name="com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity"
111-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-58
112            android:exported="false"
112-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-37
113            android:windowSoftInputMode="stateUnchanged" />
113-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:13-57
114        <activity
114-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:9-90:40
115            android:name="com.mohamedrady.v2hoor.ui.SettingsActivity"
115-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-48
116            android:exported="false" />
116-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:13-37
117        <activity
117-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:9-93:40
118            android:name="com.mohamedrady.v2hoor.ui.PerAppProxyActivity"
118-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-51
119            android:exported="false" />
119-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:13-37
120        <activity
120-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:9-96:40
121            android:name="com.mohamedrady.v2hoor.ui.ScannerActivity"
121-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-47
122            android:exported="false" />
122-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:13-37
123        <activity
123-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:9-99:40
124            android:name="com.mohamedrady.v2hoor.ui.LogcatActivity"
124-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-46
125            android:exported="false" />
125-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:13-37
126        <activity
126-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:9-102:40
127            android:name="com.mohamedrady.v2hoor.ui.RoutingSettingActivity"
127-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-54
128            android:exported="false" />
128-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:13-37
129        <activity
129-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:9-105:40
130            android:name="com.mohamedrady.v2hoor.ui.RoutingEditActivity"
130-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-51
131            android:exported="false" />
131-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:13-37
132        <activity
132-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:9-108:40
133            android:name="com.mohamedrady.v2hoor.ui.SubSettingActivity"
133-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-50
134            android:exported="false" />
134-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:13-37
135        <activity
135-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:9-111:40
136            android:name="com.mohamedrady.v2hoor.ui.UserAssetActivity"
136-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-49
137            android:exported="false" />
137-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:13-37
138        <activity
138-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:9-114:40
139            android:name="com.mohamedrady.v2hoor.ui.UserAssetUrlActivity"
139-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-52
140            android:exported="false" />
140-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:114:13-37
141        <activity
141-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:9-118:40
142            android:name="com.mohamedrady.v2hoor.ui.SubEditActivity"
142-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-47
143            android:exported="false" />
143-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:13-37
144        <activity
144-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:9-121:40
145            android:name="com.mohamedrady.v2hoor.ui.ScScannerActivity"
145-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-49
146            android:exported="false" />
146-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:13-37
147        <activity
147-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:9-127:71
148            android:name="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
148-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-48
149            android:excludeFromRecents="true"
149-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-46
150            android:exported="false"
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-37
151            android:process=":RunSoLibV2RayDaemon"
151-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-51
152            android:theme="@style/AppTheme.NoActionBar.Translucent" />
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:127:13-68
153        <activity
153-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:9-147:20
154            android:name="com.mohamedrady.v2hoor.ui.UrlSchemeActivity"
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-49
155            android:exported="true" >
155-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-36
156            <intent-filter>
156-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:13-136:29
157                <action android:name="android.intent.action.SEND" />
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-69
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:25-66
158
159                <category android:name="android.intent.category.DEFAULT" />
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
160
161                <data android:mimeType="text/plain" />
161-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
161-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:23-52
162            </intent-filter>
163            <intent-filter>
163-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:13-146:29
164                <action android:name="android.intent.action.VIEW" />
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
165
166                <category android:name="android.intent.category.BROWSABLE" />
166-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
166-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
167                <category android:name="android.intent.category.DEFAULT" />
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
168
169                <data android:scheme="v2rayng" />
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
170                <data android:host="install-config" />
170-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
170-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
171                <data android:host="install-sub" />
171-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
171-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
172            </intent-filter>
173        </activity>
174        <activity
174-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:9-150:40
175            android:name="com.mohamedrady.v2hoor.ui.CheckUpdateActivity"
175-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-51
176            android:exported="false" />
176-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:13-37
177        <activity
177-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:9-153:40
178            android:name="com.mohamedrady.v2hoor.ui.AboutActivity"
178-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-45
179            android:exported="false" />
179-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:153:13-37
180        <activity
180-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:9-157:67
181            android:name="com.mohamedrady.v2hoor.ui.LoginActivity"
181-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-45
182            android:exported="false"
182-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-37
183            android:theme="@style/AppThemeDayNight.NoActionBar" />
183-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:157:13-64
184        <activity
184-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:9-161:67
185            android:name="com.mohamedrady.v2hoor.ui.AdminPanelActivity"
185-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-50
186            android:exported="false"
186-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-37
187            android:theme="@style/AppThemeMaterial.NoActionBar" />
187-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-64
188        <activity
188-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:9-165:67
189            android:name="com.mohamedrady.v2hoor.ui.LogViewerActivity"
189-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:13-49
190            android:exported="false"
190-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:164:13-37
191            android:theme="@style/AppThemeMaterial.NoActionBar" />
191-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-64
192        <activity
192-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:9-169:67
193            android:name="com.mohamedrady.v2hoor.ui.RealTimeLogActivity"
193-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:13-51
194            android:exported="false"
194-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:168:13-37
195            android:theme="@style/AppThemeMaterial.NoActionBar" />
195-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:13-64
196        <activity
196-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:9-173:67
197            android:name="com.mohamedrady.v2hoor.ui.AdminUsersActivity"
197-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:171:13-50
198            android:exported="false"
198-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-37
199            android:theme="@style/AppThemeMaterial.NoActionBar" />
199-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:13-64
200        <activity
200-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:9-177:67
201            android:name="com.mohamedrady.v2hoor.ui.AddUserActivity"
201-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:175:13-47
202            android:exported="false"
202-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:176:13-37
203            android:theme="@style/AppThemeDayNight.NoActionBar" />
203-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:13-64
204        <activity
204-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:9-181:67
205            android:name="com.mohamedrady.v2hoor.ui.UserDetailsActivity"
205-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:179:13-51
206            android:exported="false"
206-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:180:13-37
207            android:theme="@style/AppThemeDayNight.NoActionBar" />
207-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:181:13-64
208        <activity
208-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:182:9-185:67
209            android:name="com.mohamedrady.v2hoor.ui.UserServersActivity"
209-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:183:13-51
210            android:exported="false"
210-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:184:13-37
211            android:theme="@style/AppThemeDayNight.NoActionBar" />
211-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:185:13-64
212        <activity
212-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:186:9-189:67
213            android:name="com.mohamedrady.v2hoor.ui.AdminServersActivity"
213-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:187:13-52
214            android:exported="false"
214-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:188:13-37
215            android:theme="@style/AppThemeMaterial.NoActionBar" />
215-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:13-64
216        <activity
216-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:9-193:67
217            android:name="com.mohamedrady.v2hoor.ui.ServerDetailsActivity"
217-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-53
218            android:exported="false"
218-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:192:13-37
219            android:theme="@style/AppThemeDayNight.NoActionBar" />
219-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:13-64
220        <activity
220-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:9-197:67
221            android:name="com.mohamedrady.v2hoor.ui.EditServerActivity"
221-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:13-50
222            android:exported="false"
222-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-37
223            android:theme="@style/AppThemeDayNight.NoActionBar" />
223-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:13-64
224        <activity
224-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:9-201:67
225            android:name="com.mohamedrady.v2hoor.ui.ServerUsersActivity"
225-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:13-51
226            android:exported="false"
226-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:200:13-37
227            android:theme="@style/AppThemeDayNight.NoActionBar" />
227-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:13-64
228        <activity
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:9-205:67
229            android:name="com.mohamedrady.v2hoor.ui.ServerManagementActivity"
229-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:13-56
230            android:exported="false"
230-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:204:13-37
231            android:theme="@style/AppThemeDayNight.NoActionBar" />
231-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:205:13-64
232        <activity
232-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:9-209:67
233            android:name="com.mohamedrady.v2hoor.ui.AddServerActivity"
233-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:13-49
234            android:exported="false"
234-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:208:13-37
235            android:theme="@style/AppThemeDayNight.NoActionBar" />
235-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:209:13-64
236        <activity
236-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:210:9-213:67
237            android:name="com.mohamedrady.v2hoor.ui.ServerLogsActivity"
237-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:13-50
238            android:exported="false"
238-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:212:13-37
239            android:theme="@style/AppThemeDayNight.NoActionBar" />
239-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:213:13-64
240        <activity
240-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:214:9-217:67
241            android:name="com.mohamedrady.v2hoor.ui.UserManagementActivity"
241-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:13-54
242            android:exported="false"
242-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-37
243            android:theme="@style/AppThemeDayNight.NoActionBar" />
243-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-64
244        <activity
244-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:9-221:67
245            android:name="com.mohamedrady.v2hoor.ui.PromoteUserActivity"
245-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:219:13-51
246            android:exported="false"
246-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-37
247            android:theme="@style/AppThemeDayNight.NoActionBar" />
247-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:13-64
248        <activity
248-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:222:9-225:67
249            android:name="com.mohamedrady.v2hoor.ui.SystemSettingsActivity"
249-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:223:13-54
250            android:exported="false"
250-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:224:13-37
251            android:theme="@style/AppThemeDayNight.NoActionBar" />
251-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:13-64
252
253        <service
253-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:227:9-244:19
254            android:name="com.mohamedrady.v2hoor.service.V2RayVpnService"
254-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:228:13-52
255            android:enabled="true"
255-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:229:13-35
256            android:exported="false"
256-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:230:13-37
257            android:foregroundServiceType="specialUse"
257-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:231:13-55
258            android:label="@string/app_name"
258-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:13-45
259            android:permission="android.permission.BIND_VPN_SERVICE"
259-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:13-69
260            android:process=":RunSoLibV2RayDaemon" >
260-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:234:13-51
261            <intent-filter>
261-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:235:13-237:29
262                <action android:name="android.net.VpnService" />
262-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:17-65
262-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:25-62
263            </intent-filter>
264
265            <meta-data
265-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:238:13-240:40
266                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
266-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:239:17-73
267                android:value="true" />
267-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:240:17-37
268
269            <property
269-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:13-243:39
270                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
270-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:17-76
271                android:value="vpn" />
271-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-36
272        </service>
273        <service
273-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:246:9-255:19
274            android:name="com.mohamedrady.v2hoor.service.V2RayProxyOnlyService"
274-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:13-58
275            android:exported="false"
275-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:248:13-37
276            android:foregroundServiceType="specialUse"
276-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:249:13-55
277            android:label="@string/app_name"
277-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:250:13-45
278            android:process=":RunSoLibV2RayDaemon" >
278-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:251:13-51
279            <property
279-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:13-243:39
280                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
280-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:17-76
281                android:value="proxy" />
281-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-36
282        </service>
283        <service
283-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:257:9-260:54
284            android:name="com.mohamedrady.v2hoor.service.V2RayTestService"
284-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:258:13-53
285            android:exported="false"
285-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:259:13-37
286            android:process=":RunSoLibV2RayDaemon" />
286-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:260:13-51
287
288        <receiver
288-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:262:9-274:20
289            android:name="com.mohamedrady.v2hoor.receiver.WidgetProvider"
289-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:263:13-52
290            android:exported="true"
290-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:264:13-36
291            android:process=":RunSoLibV2RayDaemon" >
291-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-51
292            <meta-data
292-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:13-268:63
293                android:name="android.appwidget.provider"
293-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:17-58
294                android:resource="@xml/app_widget_provider" />
294-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:17-60
295
296            <intent-filter>
296-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:269:13-273:29
297                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
297-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:17-84
297-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:25-81
298                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
298-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:17-85
298-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:25-82
299                <action android:name="com.mohamedrady.v2hoor.action.activity" />
299-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:272:17-81
299-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:272:25-78
300            </intent-filter>
301        </receiver>
302        <receiver
302-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:275:9-282:20
303            android:name="com.mohamedrady.v2hoor.receiver.BootReceiver"
303-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:276:13-50
304            android:exported="true"
304-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:277:13-36
305            android:label="BootReceiver" >
305-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:278:13-41
306            <intent-filter>
306-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:279:13-281:29
307                <action android:name="android.intent.action.BOOT_COMPLETED" />
307-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:17-79
307-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:25-76
308            </intent-filter>
309        </receiver>
310
311        <service
311-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:284:9-299:19
312            android:name="com.mohamedrady.v2hoor.service.QSTileService"
312-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:285:13-50
313            android:exported="true"
313-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:286:13-36
314            android:foregroundServiceType="specialUse"
314-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:287:13-55
315            android:icon="@drawable/ic_stat_name"
315-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:288:13-50
316            android:label="@string/app_tile_name"
316-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:289:13-50
317            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
317-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:290:13-77
318            android:process=":RunSoLibV2RayDaemon" >
318-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:291:13-51
319            <intent-filter>
319-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:293:13-295:29
320                <action android:name="android.service.quicksettings.action.QS_TILE" />
320-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:294:17-87
320-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:294:25-84
321            </intent-filter>
322
323            <property
323-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:13-243:39
324                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
324-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:17-76
325                android:value="tile" />
325-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-36
326        </service>
327        <!-- =====================Tasker===================== -->
328        <activity
328-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:301:9-308:20
329            android:name="com.mohamedrady.v2hoor.ui.TaskerActivity"
329-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:302:13-46
330            android:exported="true"
330-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:303:13-36
331            android:icon="@mipmap/ic_launcher" >
331-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:304:13-47
332            <intent-filter>
332-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:305:13-307:29
333                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
333-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:306:17-95
333-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:306:25-92
334            </intent-filter>
335        </activity>
336
337        <receiver
337-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:310:9-318:20
338            android:name="com.mohamedrady.v2hoor.receiver.TaskerReceiver"
338-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:311:13-52
339            android:exported="true"
339-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:312:13-36
340            android:process=":RunSoLibV2RayDaemon" >
340-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:313:13-51
341            <intent-filter>
341-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:315:13-317:29
342                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
342-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:316:17-95
342-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:316:25-92
343            </intent-filter>
344        </receiver>
345        <!-- =====================Tasker===================== -->
346        <provider
347            android:name="androidx.startup.InitializationProvider"
347-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:321:13-67
348            android:authorities="com.mohamedrady.v2hoor.androidx-startup"
348-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:322:13-68
349            android:exported="false" >
349-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:323:13-37
350            <meta-data
350-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
351                android:name="androidx.emoji2.text.EmojiCompatInitializer"
351-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
352                android:value="androidx.startup" />
352-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
353            <meta-data
353-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
354                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
354-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
355                android:value="androidx.startup" />
355-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
356            <meta-data
356-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
357                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
357-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
358                android:value="androidx.startup" />
358-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
359        </provider>
360        <provider
361            android:name="androidx.core.content.FileProvider"
361-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:334:13-62
362            android:authorities="com.mohamedrady.v2hoor.cache"
362-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:335:13-57
363            android:exported="false"
363-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:336:13-37
364            android:grantUriPermissions="true" >
364-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:337:13-47
365            <meta-data
365-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:338:13-340:55
366                android:name="android.support.FILE_PROVIDER_PATHS"
366-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:339:17-67
367                android:resource="@xml/cache_paths" />
367-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:340:17-52
368        </provider>
369
370        <activity
370-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
371            android:name="io.github.g00fy2.quickie.QRScannerActivity"
371-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
372            android:screenOrientation="behind"
372-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
373            android:theme="@style/QuickieScannerActivity" />
373-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
374        <!--
375        Service for holding metadata. Cannot be instantiated.
376        Metadata will be merged from other manifests.
377        -->
378        <service
378-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
379            android:name="androidx.camera.core.impl.MetadataHolderService"
379-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
380            android:enabled="false"
380-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
381            android:exported="false" >
381-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
382            <meta-data
382-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
383                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
383-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
384                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
384-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
385        </service>
386        <service
386-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
387            android:name="com.google.firebase.components.ComponentDiscoveryService"
387-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
388            android:directBootAware="true"
388-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
389            android:exported="false" >
389-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
390            <meta-data
390-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
391                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
391-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
392                android:value="com.google.firebase.components.ComponentRegistrar" />
392-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
393            <meta-data
393-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
394                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
394-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
395                android:value="com.google.firebase.components.ComponentRegistrar" />
395-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
396            <meta-data
396-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:11:13-13:85
397                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
397-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:12:17-129
398                android:value="com.google.firebase.components.ComponentRegistrar" />
398-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:13:17-82
399            <meta-data
399-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
400                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
400-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
401                android:value="com.google.firebase.components.ComponentRegistrar" />
401-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
402            <meta-data
402-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
403                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
403-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
404                android:value="com.google.firebase.components.ComponentRegistrar" />
404-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
405            <meta-data
405-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:12:13-14:85
406                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
406-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:13:17-129
407                android:value="com.google.firebase.components.ComponentRegistrar" />
407-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:14:17-82
408            <meta-data
408-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
409                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
409-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
410                android:value="com.google.firebase.components.ComponentRegistrar" />
410-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
411            <meta-data
411-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
412                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
412-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
413                android:value="com.google.firebase.components.ComponentRegistrar" />
413-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
414            <meta-data
414-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
415                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
415-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
416                android:value="com.google.firebase.components.ComponentRegistrar" />
416-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
417            <meta-data
417-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
418                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
418-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
419                android:value="com.google.firebase.components.ComponentRegistrar" />
419-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
420            <meta-data
420-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
421                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
421-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
422                android:value="com.google.firebase.components.ComponentRegistrar" />
422-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
423            <meta-data
423-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
424                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
424-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
425                android:value="com.google.firebase.components.ComponentRegistrar" />
425-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
426            <meta-data
426-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
427                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
427-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
428                android:value="com.google.firebase.components.ComponentRegistrar" />
428-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
429            <meta-data
429-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
430                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
430-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
431                android:value="com.google.firebase.components.ComponentRegistrar" />
431-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
432        </service>
433
434        <activity
434-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
435            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
435-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
436            android:excludeFromRecents="true"
436-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
437            android:exported="true"
437-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
438            android:launchMode="singleTask"
438-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
439            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
439-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
440            <intent-filter>
440-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
441                <action android:name="android.intent.action.VIEW" />
441-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
441-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
442
443                <category android:name="android.intent.category.DEFAULT" />
443-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
443-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
444                <category android:name="android.intent.category.BROWSABLE" />
444-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
444-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
445
446                <data
446-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
447                    android:host="firebase.auth"
447-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
448                    android:path="/"
449                    android:scheme="genericidp" />
449-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
450            </intent-filter>
451        </activity>
452        <activity
452-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
453            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
453-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
454            android:excludeFromRecents="true"
454-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
455            android:exported="true"
455-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
456            android:launchMode="singleTask"
456-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
457            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
457-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
458            <intent-filter>
458-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
459                <action android:name="android.intent.action.VIEW" />
459-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
459-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
460
461                <category android:name="android.intent.category.DEFAULT" />
461-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
461-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
462                <category android:name="android.intent.category.BROWSABLE" />
462-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
462-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
463
464                <data
464-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
465                    android:host="firebase.auth"
465-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
466                    android:path="/"
467                    android:scheme="recaptcha" />
467-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
468            </intent-filter>
469        </activity>
470
471        <service
471-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
472            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
472-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
473            android:enabled="true"
473-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
474            android:exported="false" >
474-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
475            <meta-data
475-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
476                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
476-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
477                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
477-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
478        </service>
479
480        <activity
480-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
481            android:name="androidx.credentials.playservices.HiddenActivity"
481-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
482            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
482-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
483            android:enabled="true"
483-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
484            android:exported="false"
484-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
485            android:fitsSystemWindows="true"
485-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
486            android:theme="@style/Theme.Hidden" >
486-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
487        </activity>
488        <activity
488-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
489            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
489-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
490            android:excludeFromRecents="true"
490-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
491            android:exported="false"
491-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
492            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
492-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
493        <!--
494            Service handling Google Sign-In user revocation. For apps that do not integrate with
495            Google Sign-In, this service will never be started.
496        -->
497        <service
497-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
498            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
498-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
499            android:exported="true"
499-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
500            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
500-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
501            android:visibleToInstantApps="true" />
501-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
502
503        <receiver
503-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
504            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
504-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
505            android:enabled="true"
505-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
506            android:exported="false" >
506-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
507        </receiver>
508
509        <service
509-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
510            android:name="com.google.android.gms.measurement.AppMeasurementService"
510-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
511            android:enabled="true"
511-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
512            android:exported="false" />
512-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
513        <service
513-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
514            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
514-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
515            android:enabled="true"
515-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
516            android:exported="false"
516-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
517            android:permission="android.permission.BIND_JOB_SERVICE" />
517-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
518
519        <provider
519-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
520            android:name="com.google.firebase.provider.FirebaseInitProvider"
520-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
521            android:authorities="com.mohamedrady.v2hoor.firebaseinitprovider"
521-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
522            android:directBootAware="true"
522-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
523            android:exported="false"
523-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
524            android:initOrder="100" />
524-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
525
526        <service
526-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
527            android:name="androidx.work.multiprocess.RemoteWorkManagerService"
527-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
528            android:exported="false" />
528-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
529        <service
529-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
530            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
530-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
531            android:directBootAware="false"
531-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
532            android:enabled="@bool/enable_system_alarm_service_default"
532-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
533            android:exported="false" />
533-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
534        <service
534-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
535            android:name="androidx.work.impl.background.systemjob.SystemJobService"
535-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
536            android:directBootAware="false"
536-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
537            android:enabled="@bool/enable_system_job_service_default"
537-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
538            android:exported="true"
538-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
539            android:permission="android.permission.BIND_JOB_SERVICE" />
539-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
540        <service
540-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
541            android:name="androidx.work.impl.foreground.SystemForegroundService"
541-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
542            android:directBootAware="false"
542-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
543            android:enabled="@bool/enable_system_foreground_service_default"
543-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
544            android:exported="false" />
544-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
545
546        <receiver
546-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
547            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
547-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
548            android:directBootAware="false"
548-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
549            android:enabled="true"
549-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
550            android:exported="false" />
550-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
551        <receiver
551-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
552            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
552-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
553            android:directBootAware="false"
553-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
554            android:enabled="false"
554-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
555            android:exported="false" >
555-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
556            <intent-filter>
556-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
557                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
557-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
557-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
558                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
558-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
558-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
559            </intent-filter>
560        </receiver>
561        <receiver
561-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
562            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
562-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
563            android:directBootAware="false"
563-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
564            android:enabled="false"
564-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
565            android:exported="false" >
565-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
566            <intent-filter>
566-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
567                <action android:name="android.intent.action.BATTERY_OKAY" />
567-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
567-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
568                <action android:name="android.intent.action.BATTERY_LOW" />
568-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
568-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
569            </intent-filter>
570        </receiver>
571        <receiver
571-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
572            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
572-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
573            android:directBootAware="false"
573-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
574            android:enabled="false"
574-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
575            android:exported="false" >
575-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
576            <intent-filter>
576-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
577                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
577-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
577-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
578                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
578-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
578-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
579            </intent-filter>
580        </receiver>
581        <receiver
581-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
582            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
582-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
583            android:directBootAware="false"
583-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
584            android:enabled="false"
584-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
585            android:exported="false" >
585-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
586            <intent-filter>
586-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
587                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
587-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
587-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
588            </intent-filter>
589        </receiver>
590        <receiver
590-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
591            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
591-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
592            android:directBootAware="false"
592-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
593            android:enabled="false"
593-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
594            android:exported="false" >
594-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
595            <intent-filter>
595-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
596                <action android:name="android.intent.action.BOOT_COMPLETED" />
596-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:17-79
596-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:25-76
597                <action android:name="android.intent.action.TIME_SET" />
597-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
597-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
598                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
598-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
598-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
599            </intent-filter>
600        </receiver>
601        <receiver
601-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
602            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
602-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
603            android:directBootAware="false"
603-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
604            android:enabled="@bool/enable_system_alarm_service_default"
604-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
605            android:exported="false" >
605-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
606            <intent-filter>
606-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
607                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
607-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
607-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
608            </intent-filter>
609        </receiver>
610        <receiver
610-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
611            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
611-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
612            android:directBootAware="false"
612-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
613            android:enabled="true"
613-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
614            android:exported="true"
614-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
615            android:permission="android.permission.DUMP" >
615-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
616            <intent-filter>
616-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
617                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
617-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
617-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
618            </intent-filter>
619        </receiver>
620
621        <uses-library
621-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
622            android:name="androidx.window.extensions"
622-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
623            android:required="false" />
623-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
624        <uses-library
624-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
625            android:name="androidx.window.sidecar"
625-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
626            android:required="false" />
626-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
627
628        <activity
628-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
629            android:name="com.google.android.gms.common.api.GoogleApiActivity"
629-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
630            android:exported="false"
630-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
631            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
631-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
632
633        <service
633-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
634            android:name="androidx.room.MultiInstanceInvalidationService"
634-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
635            android:directBootAware="true"
635-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
636            android:exported="false" />
636-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
637
638        <uses-library
638-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
639            android:name="android.ext.adservices"
639-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
640            android:required="false" />
640-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
641
642        <receiver
642-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
643            android:name="androidx.profileinstaller.ProfileInstallReceiver"
643-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
644            android:directBootAware="false"
644-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
645            android:enabled="true"
645-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
646            android:exported="true"
646-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
647            android:permission="android.permission.DUMP" >
647-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
648            <intent-filter>
648-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
649                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
649-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
649-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
650            </intent-filter>
651            <intent-filter>
651-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
652                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
652-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
652-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
653            </intent-filter>
654            <intent-filter>
654-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
655                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
655-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
655-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
656            </intent-filter>
657            <intent-filter>
657-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
658                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
658-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
658-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
659            </intent-filter>
660        </receiver>
661
662        <meta-data
662-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
663            android:name="com.google.android.gms.version"
663-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
664            android:value="@integer/google_play_services_version" />
664-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
665
666        <activity
666-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
667            android:name="com.journeyapps.barcodescanner.CaptureActivity"
667-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
668            android:clearTaskOnLaunch="true"
668-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
669            android:screenOrientation="sensorLandscape"
669-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
670            android:stateNotNeeded="true"
670-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
671            android:theme="@style/zxing_CaptureTheme"
671-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
672            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- The activities will be merged into the manifest of the hosting app. -->
672-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
673        <activity
673-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
674            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
674-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
675            android:exported="false"
675-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
676            android:stateNotNeeded="true"
676-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
677            android:theme="@style/Theme.PlayCore.Transparent" />
677-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
678    </application>
679
680</manifest>
