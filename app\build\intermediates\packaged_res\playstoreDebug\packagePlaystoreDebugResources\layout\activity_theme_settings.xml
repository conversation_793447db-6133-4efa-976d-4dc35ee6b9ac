<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".ui.ThemeSettingsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="@string/theme_settings"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_arrow_back_24dp" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Current Theme Status -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:id="@+id/iv_theme_indicator"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_light_mode_24dp"
                        android:tint="?attr/colorPrimary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_current_theme"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="المظهر الحالي: تلقائي"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:id="@+id/tv_theme_status"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="المظهر الفاتح نشط"
                            android:textSize="14sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_toggle_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تبديل"
                        android:textSize="12sp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Theme Selection -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اختيار المظهر"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="12dp" />

                    <Spinner
                        android:id="@+id/spinner_theme_mode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp" />

                    <!-- Theme Preview Buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_preview_light"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="معاينة فاتح"
                            android:textSize="12sp"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                        <Button
                            android:id="@+id/btn_preview_dark"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="معاينة داكن"
                            android:textSize="12sp"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Auto Schedule Settings -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الجدولة التلقائية"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorOnSurface" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_auto_schedule"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تغيير المظهر تلقائياً حسب الوقت"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="16dp" />

                    <!-- Schedule Time Settings -->
                    <LinearLayout
                        android:id="@+id/layout_schedule_settings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <Button
                                android:id="@+id/btn_schedule_start"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginEnd="8dp"
                                android:text="البداية: 22:00"
                                android:textSize="12sp"
                                style="@style/Widget.Material3.Button.OutlinedButton" />

                            <Button
                                android:id="@+id/btn_schedule_end"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginStart="8dp"
                                android:text="النهاية: 06:00"
                                android:textSize="12sp"
                                style="@style/Widget.Material3.Button.OutlinedButton" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="من "
                                android:textSize="14sp"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                            <TextView
                                android:id="@+id/tv_schedule_start"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="22:00"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=" إلى "
                                android:textSize="14sp"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                            <TextView
                                android:id="@+id/tv_schedule_end"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="06:00"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorPrimary" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Theme Statistics -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إحصائيات المظهر"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_stats_current_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المظهر: تلقائي"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_stats_dark_active"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الوضع الداكن: لا"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_stats_auto_schedule"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الجدولة: معطل"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_stats_schedule_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="التوقيت: غير محدد"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Reset Button -->
            <Button
                android:id="@+id/btn_reset_theme"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="إعادة تعيين إعدادات المظهر"
                android:textColor="?attr/colorError"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
