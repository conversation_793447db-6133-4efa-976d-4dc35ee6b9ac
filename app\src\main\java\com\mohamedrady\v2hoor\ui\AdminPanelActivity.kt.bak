package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdminPanelBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.service.AdminPermissionService
import com.mohamedrady.v2hoor.service.AutoServerUpdater
import com.mohamedrady.v2hoor.service.FirebaseAuthService
import com.mohamedrady.v2hoor.service.AdminManagementService
import com.mohamedrady.v2hoor.viewmodel.AdminViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AdminPanelActivity : BaseActivity() {

    private lateinit var binding: ActivityAdminPanelBinding
    private val adminViewModel: AdminViewModel by viewModels()
    private lateinit var adminService: AdminPermissionService
    private lateinit var authService: FirebaseAuthService
    private lateinit var adminManagementService: AdminManagementService

    override fun onCreate(savedInstanceState: Bundle?) {
        // Set theme before calling super.onCreate()
        setTheme(R.style.AppThemeMaterial_NoActionBar)
        super.onCreate(savedInstanceState)

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "🚀 Starting AdminPanelActivity onCreate")

        try {
            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "📱 Inflating layout")
            binding = ActivityAdminPanelBinding.inflate(layoutInflater)
            setContentView(binding.root)

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "🔧 Initializing services")
            adminService = AdminPermissionService.getInstance()
            authService = FirebaseAuthService.getInstance()
            adminManagementService = AdminManagementService.getInstance(this)

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "🎨 Setting up UI components")
            setupToolbar()
            checkAdminPermissions()
            setupClickListeners()
            loadAdminInfo()

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "✅ AdminPanelActivity onCreate completed successfully")
        } catch (e: Exception) {
            com.mohamedrady.v2hoor.util.CrashHandler.logError("AdminPanel", "💥 Error in AdminPanelActivity onCreate", e)
            toastError("خطأ في تحميل لوحة الإدارة")
            finish()
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.admin_panel_title)
    }

    private fun checkAdminPermissions() {
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "🔐 Starting admin permission check")

        lifecycleScope.launch {
            try {
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "👤 Checking if user is admin")
                val isAdmin = adminService.isAdmin()
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "🔍 Admin check result: $isAdmin")

                if (!isAdmin) {
                    com.mohamedrady.v2hoor.util.CrashHandler.logWarning("AdminPanel", "🚫 Access denied - user is not admin")
                    toastError("ليس لديك صلاحيات للوصول لهذه الصفحة")
                    finish()
                    return@launch
                }

                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "✅ Admin access granted, getting permissions")
                val permissions = adminService.getCurrentUserPermissions()
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "📋 Permissions loaded successfully")

                updateUIBasedOnPermissions(permissions)
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminPanel", "🎨 UI updated based on permissions")
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("AdminPanel", "💥 Error checking admin permissions", e)
                toastError("خطأ في التحقق من الصلاحيات")
                finish()
            }
        }
    }

    private fun updateUIBasedOnPermissions(permissions: AdminPermissionService.AdminPermissions) {
        binding.apply {
            // Server management
            cardServerManagement.visibility = if (permissions.canManageServers) View.VISIBLE else View.GONE
            cardAddServer.visibility = if (permissions.canAddServers) View.VISIBLE else View.GONE
            cardServerLogs.visibility = if (permissions.canViewLogs) View.VISIBLE else View.GONE
            
            // User management
            cardUserManagement.visibility = if (permissions.canManageUsers) View.VISIBLE else View.GONE
            cardPromoteUser.visibility = if (permissions.canPromoteUsers) View.VISIBLE else View.GONE
            
            // System settings
            cardSystemSettings.visibility = if (permissions.canManageSettings) View.VISIBLE else View.GONE
            
            // Always visible for admins
            cardServerUpdate.visibility = View.VISIBLE
            cardAdminInfo.visibility = View.VISIBLE
        }
    }

    private fun setupClickListeners() {
        binding.apply {
            // Server management
            cardServerManagement.setOnClickListener {
                com.mohamedrady.v2hoor.util.ErrorMonitor.logUserAction("Navigate to Server Management")
                com.mohamedrady.v2hoor.util.ErrorMonitor.logActivityTransition("AdminPanel", "AdminServers")
                startActivity(Intent(this@AdminPanelActivity, AdminServersActivity::class.java))
            }

            cardAddServer.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, AddServerActivity::class.java))
            }

            cardServerUpdate.setOnClickListener {
                performManualServerUpdate()
            }

            cardServerLogs.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, ServerLogsActivity::class.java))
            }

            // User management
            cardUserManagement.setOnClickListener {
                com.mohamedrady.v2hoor.util.ErrorMonitor.logUserAction("Navigate to User Management")
                com.mohamedrady.v2hoor.util.ErrorMonitor.logActivityTransition("AdminPanel", "AdminUsers")
                startActivity(Intent(this@AdminPanelActivity, AdminUsersActivity::class.java))
            }

            cardPromoteUser.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, PromoteUserActivity::class.java))
            }

            // System settings
            cardSystemSettings.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, SystemSettingsActivity::class.java))
            }

            // View logs
            cardViewLogs.setOnClickListener {
                com.mohamedrady.v2hoor.util.ErrorMonitor.logUserAction("Navigate to Log Viewer")
                com.mohamedrady.v2hoor.util.ErrorMonitor.logActivityTransition("AdminPanel", "LogViewer")
                startActivity(Intent(this@AdminPanelActivity, LogViewerActivity::class.java))
            }

            // Real-time logs
            cardRealTimeLogs.setOnClickListener {
                com.mohamedrady.v2hoor.util.ErrorMonitor.logUserAction("Navigate to Real-time Log Monitor")
                com.mohamedrady.v2hoor.util.ErrorMonitor.logActivityTransition("AdminPanel", "RealTimeLog")
                startActivity(Intent(this@AdminPanelActivity, RealTimeLogActivity::class.java))
            }

            // Admin info
            cardAdminInfo.setOnClickListener {
                showAdminInfo()
            }
        }
    }

    private fun loadAdminInfo() {
        lifecycleScope.launch {
            try {
                val user = authService.getCurrentUser()
                val level = adminService.getCurrentUserAdminLevel()
                val permissions = adminService.getCurrentUserPermissions()

                withContext(Dispatchers.Main) {
                    binding.apply {
                        tvAdminEmail.text = user?.email ?: "غير محدد"
                        tvAdminLevel.text = when (level) {
                            AdminPermissionService.AdminLevel.SUPER_ADMIN -> "مدير عام"
                            AdminPermissionService.AdminLevel.ADMIN -> "مدير"
                            AdminPermissionService.AdminLevel.MODERATOR -> "مشرف"
                            else -> "مستخدم عادي"
                        }

                        // Show permissions count
                        val permissionCount = listOf(
                            permissions.canManageServers,
                            permissions.canManageUsers,
                            permissions.canViewLogs,
                            permissions.canManageSettings,
                            permissions.canPromoteUsers,
                            permissions.canDeleteServers,
                            permissions.canAddServers,
                            permissions.canViewServerDetails,
                            permissions.canEditServerDetails
                        ).count { it }

                        tvPermissionsCount.text = "الصلاحيات: $permissionCount/9"
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    toastError("فشل في تحميل معلومات المدير")
                }
            }
        }
    }

    private fun performManualServerUpdate() {
        binding.progressBar.visibility = View.VISIBLE
        binding.tvUpdateStatus.text = "جاري تحديث السيرفرات..."

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = AutoServerUpdater.manualUpdate(this@AdminPanelActivity)
                
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    
                    if (result.isSuccess) {
                        val count = result.getOrNull() ?: 0
                        if (count > 0) {
                            binding.tvUpdateStatus.text = "تم تحديث $count سيرفر بنجاح"
                            toast("تم تحديث $count سيرفر بنجاح")
                        } else {
                            binding.tvUpdateStatus.text = "لا توجد سيرفرات جديدة للتحديث"
                            toast("لا توجد سيرفرات جديدة للتحديث")
                        }
                    } else {
                        binding.tvUpdateStatus.text = "فشل في تحديث السيرفرات"
                        toastError("فشل في تحديث السيرفرات")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    binding.tvUpdateStatus.text = "خطأ في تحديث السيرفرات"
                    toastError("خطأ في تحديث السيرفرات")
                }
            }
        }
    }

    private fun showAdminInfo() {
        lifecycleScope.launch {
            val level = adminService.getCurrentUserAdminLevel()
            val permissions = adminService.getCurrentUserPermissions()
            val isSuperAdmin = adminService.isSuperAdmin()

            val info = buildString {
                appendLine("مستوى الإدارة: ${when (level) {
                    AdminPermissionService.AdminLevel.SUPER_ADMIN -> "مدير عام"
                    AdminPermissionService.AdminLevel.ADMIN -> "مدير"
                    AdminPermissionService.AdminLevel.MODERATOR -> "مشرف"
                    else -> "مستخدم عادي"
                }}")
                
                if (isSuperAdmin) {
                    appendLine("🔥 مدير عام - صلاحيات كاملة")
                }
                
                appendLine("\nالصلاحيات:")
                if (permissions.canManageServers) appendLine("✅ إدارة السيرفرات")
                if (permissions.canManageUsers) appendLine("✅ إدارة المستخدمين")
                if (permissions.canViewLogs) appendLine("✅ عرض السجلات")
                if (permissions.canManageSettings) appendLine("✅ إدارة الإعدادات")
                if (permissions.canPromoteUsers) appendLine("✅ ترقية المستخدمين")
                if (permissions.canDeleteServers) appendLine("✅ حذف السيرفرات")
                if (permissions.canAddServers) appendLine("✅ إضافة السيرفرات")
                if (permissions.canViewServerDetails) appendLine("✅ عرض تفاصيل السيرفرات")
                if (permissions.canEditServerDetails) appendLine("✅ تعديل تفاصيل السيرفرات")
            }

            withContext(Dispatchers.Main) {
                androidx.appcompat.app.AlertDialog.Builder(this@AdminPanelActivity)
                    .setTitle("معلومات المدير")
                    .setMessage(info)
                    .setPositiveButton("موافق", null)
                    .show()
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
