// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityThemeSettingsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final Button btnPreviewDark;

  @NonNull
  public final Button btnPreviewLight;

  @NonNull
  public final Button btnResetTheme;

  @NonNull
  public final Button btnScheduleEnd;

  @NonNull
  public final Button btnScheduleStart;

  @NonNull
  public final Button btnToggleTheme;

  @NonNull
  public final ImageView ivThemeIndicator;

  @NonNull
  public final LinearLayout layoutScheduleSettings;

  @NonNull
  public final Spinner spinnerThemeMode;

  @NonNull
  public final SwitchMaterial switchAutoSchedule;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCurrentTheme;

  @NonNull
  public final TextView tvScheduleEnd;

  @NonNull
  public final TextView tvScheduleStart;

  @NonNull
  public final TextView tvStatsAutoSchedule;

  @NonNull
  public final TextView tvStatsCurrentTheme;

  @NonNull
  public final TextView tvStatsDarkActive;

  @NonNull
  public final TextView tvStatsScheduleTime;

  @NonNull
  public final TextView tvThemeStatus;

  private ActivityThemeSettingsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull Button btnPreviewDark,
      @NonNull Button btnPreviewLight, @NonNull Button btnResetTheme,
      @NonNull Button btnScheduleEnd, @NonNull Button btnScheduleStart,
      @NonNull Button btnToggleTheme, @NonNull ImageView ivThemeIndicator,
      @NonNull LinearLayout layoutScheduleSettings, @NonNull Spinner spinnerThemeMode,
      @NonNull SwitchMaterial switchAutoSchedule, @NonNull Toolbar toolbar,
      @NonNull TextView tvCurrentTheme, @NonNull TextView tvScheduleEnd,
      @NonNull TextView tvScheduleStart, @NonNull TextView tvStatsAutoSchedule,
      @NonNull TextView tvStatsCurrentTheme, @NonNull TextView tvStatsDarkActive,
      @NonNull TextView tvStatsScheduleTime, @NonNull TextView tvThemeStatus) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.btnPreviewDark = btnPreviewDark;
    this.btnPreviewLight = btnPreviewLight;
    this.btnResetTheme = btnResetTheme;
    this.btnScheduleEnd = btnScheduleEnd;
    this.btnScheduleStart = btnScheduleStart;
    this.btnToggleTheme = btnToggleTheme;
    this.ivThemeIndicator = ivThemeIndicator;
    this.layoutScheduleSettings = layoutScheduleSettings;
    this.spinnerThemeMode = spinnerThemeMode;
    this.switchAutoSchedule = switchAutoSchedule;
    this.toolbar = toolbar;
    this.tvCurrentTheme = tvCurrentTheme;
    this.tvScheduleEnd = tvScheduleEnd;
    this.tvScheduleStart = tvScheduleStart;
    this.tvStatsAutoSchedule = tvStatsAutoSchedule;
    this.tvStatsCurrentTheme = tvStatsCurrentTheme;
    this.tvStatsDarkActive = tvStatsDarkActive;
    this.tvStatsScheduleTime = tvStatsScheduleTime;
    this.tvThemeStatus = tvThemeStatus;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityThemeSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityThemeSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_theme_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityThemeSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar_layout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.btn_preview_dark;
      Button btnPreviewDark = ViewBindings.findChildViewById(rootView, id);
      if (btnPreviewDark == null) {
        break missingId;
      }

      id = R.id.btn_preview_light;
      Button btnPreviewLight = ViewBindings.findChildViewById(rootView, id);
      if (btnPreviewLight == null) {
        break missingId;
      }

      id = R.id.btn_reset_theme;
      Button btnResetTheme = ViewBindings.findChildViewById(rootView, id);
      if (btnResetTheme == null) {
        break missingId;
      }

      id = R.id.btn_schedule_end;
      Button btnScheduleEnd = ViewBindings.findChildViewById(rootView, id);
      if (btnScheduleEnd == null) {
        break missingId;
      }

      id = R.id.btn_schedule_start;
      Button btnScheduleStart = ViewBindings.findChildViewById(rootView, id);
      if (btnScheduleStart == null) {
        break missingId;
      }

      id = R.id.btn_toggle_theme;
      Button btnToggleTheme = ViewBindings.findChildViewById(rootView, id);
      if (btnToggleTheme == null) {
        break missingId;
      }

      id = R.id.iv_theme_indicator;
      ImageView ivThemeIndicator = ViewBindings.findChildViewById(rootView, id);
      if (ivThemeIndicator == null) {
        break missingId;
      }

      id = R.id.layout_schedule_settings;
      LinearLayout layoutScheduleSettings = ViewBindings.findChildViewById(rootView, id);
      if (layoutScheduleSettings == null) {
        break missingId;
      }

      id = R.id.spinner_theme_mode;
      Spinner spinnerThemeMode = ViewBindings.findChildViewById(rootView, id);
      if (spinnerThemeMode == null) {
        break missingId;
      }

      id = R.id.switch_auto_schedule;
      SwitchMaterial switchAutoSchedule = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoSchedule == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_current_theme;
      TextView tvCurrentTheme = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentTheme == null) {
        break missingId;
      }

      id = R.id.tv_schedule_end;
      TextView tvScheduleEnd = ViewBindings.findChildViewById(rootView, id);
      if (tvScheduleEnd == null) {
        break missingId;
      }

      id = R.id.tv_schedule_start;
      TextView tvScheduleStart = ViewBindings.findChildViewById(rootView, id);
      if (tvScheduleStart == null) {
        break missingId;
      }

      id = R.id.tv_stats_auto_schedule;
      TextView tvStatsAutoSchedule = ViewBindings.findChildViewById(rootView, id);
      if (tvStatsAutoSchedule == null) {
        break missingId;
      }

      id = R.id.tv_stats_current_theme;
      TextView tvStatsCurrentTheme = ViewBindings.findChildViewById(rootView, id);
      if (tvStatsCurrentTheme == null) {
        break missingId;
      }

      id = R.id.tv_stats_dark_active;
      TextView tvStatsDarkActive = ViewBindings.findChildViewById(rootView, id);
      if (tvStatsDarkActive == null) {
        break missingId;
      }

      id = R.id.tv_stats_schedule_time;
      TextView tvStatsScheduleTime = ViewBindings.findChildViewById(rootView, id);
      if (tvStatsScheduleTime == null) {
        break missingId;
      }

      id = R.id.tv_theme_status;
      TextView tvThemeStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvThemeStatus == null) {
        break missingId;
      }

      return new ActivityThemeSettingsBinding((CoordinatorLayout) rootView, appBarLayout,
          btnPreviewDark, btnPreviewLight, btnResetTheme, btnScheduleEnd, btnScheduleStart,
          btnToggleTheme, ivThemeIndicator, layoutScheduleSettings, spinnerThemeMode,
          switchAutoSchedule, toolbar, tvCurrentTheme, tvScheduleEnd, tvScheduleStart,
          tvStatsAutoSchedule, tvStatsCurrentTheme, tvStatsDarkActive, tvStatsScheduleTime,
          tvThemeStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
