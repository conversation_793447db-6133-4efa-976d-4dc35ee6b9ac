<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/subscription_banner"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="@drawable/subscription_banner_background"
    android:gravity="center_vertical"
    android:visibility="gone">

    <!-- Status Icon -->
    <ImageView
        android:id="@+id/iv_subscription_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/ic_subscription_active"
        android:contentDescription="@string/subscription_status_icon" />

    <!-- Status Text Container -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Main Status Text -->
        <TextView
            android:id="@+id/tv_subscription_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/subscription_active"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/subscription_text_color" />

        <!-- Details Text -->
        <TextView
            android:id="@+id/tv_subscription_details"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/subscription_details"
            android:textSize="12sp"
            android:textColor="@color/subscription_details_color"
            android:layout_marginTop="2dp" />

        <!-- Data Usage Progress (for limited subscriptions) -->
        <LinearLayout
            android:id="@+id/layout_data_usage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp"
            android:visibility="gone">

            <ProgressBar
                android:id="@+id/progress_data_usage"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:max="100"
                android:progress="0" />

            <TextView
                android:id="@+id/tv_data_usage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0/10 GB"
                android:textSize="10sp"
                android:textColor="@color/subscription_details_color"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Action Button -->
    <Button
        android:id="@+id/btn_subscription_action"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:text="@string/renew"
        android:textSize="12sp"
        android:paddingHorizontal="12dp"
        android:paddingVertical="4dp"
        android:background="@drawable/subscription_action_button_background"
        android:textColor="@android:color/white"
        android:visibility="gone" />

    <!-- Close Button -->
    <ImageButton
        android:id="@+id/btn_close_banner"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="8dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_close_24dp"
        android:contentDescription="@string/close_banner"
        android:visibility="gone" />

</LinearLayout>
