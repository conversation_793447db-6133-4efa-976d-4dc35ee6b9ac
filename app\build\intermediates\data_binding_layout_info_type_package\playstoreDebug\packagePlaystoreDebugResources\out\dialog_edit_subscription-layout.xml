<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_edit_subscription" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\dialog_edit_subscription.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_edit_subscription_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="173" endOffset="14"/></Target><Target id="@+id/et_subscription_end" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="18" startOffset="8" endLine="23" endOffset="39"/></Target><Target id="@+id/et_subscription_type" view="AutoCompleteTextView"><Expressions/><location startLine="35" startOffset="8" endLine="40" endOffset="34"/></Target><Target id="@+id/et_data_limit" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="54" startOffset="8" endLine="59" endOffset="32"/></Target><Target id="@+id/et_max_devices" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="71" startOffset="8" endLine="76" endOffset="30"/></Target><Target id="@+id/et_notes" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="88" startOffset="8" endLine="94" endOffset="46"/></Target><Target id="@+id/btn_extend_1_month" view="Button"><Expressions/><location startLine="113" startOffset="8" endLine="121" endOffset="76"/></Target><Target id="@+id/btn_extend_3_months" view="Button"><Expressions/><location startLine="123" startOffset="8" endLine="131" endOffset="76"/></Target><Target id="@+id/btn_extend_1_year" view="Button"><Expressions/><location startLine="133" startOffset="8" endLine="141" endOffset="76"/></Target><Target id="@+id/btn_unlimited" view="Button"><Expressions/><location startLine="150" startOffset="8" endLine="158" endOffset="76"/></Target><Target id="@+id/btn_deactivate" view="Button"><Expressions/><location startLine="160" startOffset="8" endLine="169" endOffset="76"/></Target></Targets></Layout>