// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditSubscriptionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnDeactivate;

  @NonNull
  public final Button btnExtend1Month;

  @NonNull
  public final Button btnExtend1Year;

  @NonNull
  public final Button btnExtend3Months;

  @NonNull
  public final Button btnUnlimited;

  @NonNull
  public final TextInputEditText etDataLimit;

  @NonNull
  public final TextInputEditText etMaxDevices;

  @NonNull
  public final TextInputEditText etNotes;

  @NonNull
  public final TextInputEditText etSubscriptionEnd;

  @NonNull
  public final AutoCompleteTextView etSubscriptionType;

  private DialogEditSubscriptionBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnDeactivate, @NonNull Button btnExtend1Month,
      @NonNull Button btnExtend1Year, @NonNull Button btnExtend3Months,
      @NonNull Button btnUnlimited, @NonNull TextInputEditText etDataLimit,
      @NonNull TextInputEditText etMaxDevices, @NonNull TextInputEditText etNotes,
      @NonNull TextInputEditText etSubscriptionEnd,
      @NonNull AutoCompleteTextView etSubscriptionType) {
    this.rootView = rootView;
    this.btnDeactivate = btnDeactivate;
    this.btnExtend1Month = btnExtend1Month;
    this.btnExtend1Year = btnExtend1Year;
    this.btnExtend3Months = btnExtend3Months;
    this.btnUnlimited = btnUnlimited;
    this.etDataLimit = etDataLimit;
    this.etMaxDevices = etMaxDevices;
    this.etNotes = etNotes;
    this.etSubscriptionEnd = etSubscriptionEnd;
    this.etSubscriptionType = etSubscriptionType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditSubscriptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditSubscriptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_subscription, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditSubscriptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_deactivate;
      Button btnDeactivate = ViewBindings.findChildViewById(rootView, id);
      if (btnDeactivate == null) {
        break missingId;
      }

      id = R.id.btn_extend_1_month;
      Button btnExtend1Month = ViewBindings.findChildViewById(rootView, id);
      if (btnExtend1Month == null) {
        break missingId;
      }

      id = R.id.btn_extend_1_year;
      Button btnExtend1Year = ViewBindings.findChildViewById(rootView, id);
      if (btnExtend1Year == null) {
        break missingId;
      }

      id = R.id.btn_extend_3_months;
      Button btnExtend3Months = ViewBindings.findChildViewById(rootView, id);
      if (btnExtend3Months == null) {
        break missingId;
      }

      id = R.id.btn_unlimited;
      Button btnUnlimited = ViewBindings.findChildViewById(rootView, id);
      if (btnUnlimited == null) {
        break missingId;
      }

      id = R.id.et_data_limit;
      TextInputEditText etDataLimit = ViewBindings.findChildViewById(rootView, id);
      if (etDataLimit == null) {
        break missingId;
      }

      id = R.id.et_max_devices;
      TextInputEditText etMaxDevices = ViewBindings.findChildViewById(rootView, id);
      if (etMaxDevices == null) {
        break missingId;
      }

      id = R.id.et_notes;
      TextInputEditText etNotes = ViewBindings.findChildViewById(rootView, id);
      if (etNotes == null) {
        break missingId;
      }

      id = R.id.et_subscription_end;
      TextInputEditText etSubscriptionEnd = ViewBindings.findChildViewById(rootView, id);
      if (etSubscriptionEnd == null) {
        break missingId;
      }

      id = R.id.et_subscription_type;
      AutoCompleteTextView etSubscriptionType = ViewBindings.findChildViewById(rootView, id);
      if (etSubscriptionType == null) {
        break missingId;
      }

      return new DialogEditSubscriptionBinding((LinearLayout) rootView, btnDeactivate,
          btnExtend1Month, btnExtend1Year, btnExtend3Months, btnUnlimited, etDataLimit,
          etMaxDevices, etNotes, etSubscriptionEnd, etSubscriptionType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
